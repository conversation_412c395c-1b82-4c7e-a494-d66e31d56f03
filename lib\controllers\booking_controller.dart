
import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../models/ticket_model.dart';
import '../services/theater_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class BookingController extends GetxController {
  
  final TheaterService _theaterService = TheaterService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs; 
  final RxList<ShowtimeModel> showtimes =
      <ShowtimeModel>[].obs; 
  final Rx<ScreenModel?> selectedScreen =
      Rx<ScreenModel?>(null); 
  final RxList<String> selectedSeats = <String>[].obs; 
  final RxDouble totalPrice = 0.0.obs; 
  final RxBool isLoading = false.obs; 
  final RxString errorMessage = ''.obs; // Thông báo lỗi

  
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null); 
  final Rx<TheaterModel?> selectedTheater =
      Rx<TheaterModel?>(null); 
  final Rx<ShowtimeModel?> selectedShowtime =
      Rx<ShowtimeModel?>(null); 

  @override
  void onInit() {
    super.onInit();
    loadTheaters(); 
  }

  
  Future<void> loadTheaters({bool activeOnly = true}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final theaterList =
          await _theaterService.getAllTheaters(activeOnly: activeOnly);
      theaters.value = theaterList;
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoading.value = false;
    }
  }

  
  Future<void> loadShowtimes(int movieId, String theaterId,
      {String? date}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      print(
          'BookingController.loadShowtimes: movieId=$movieId, theaterId=$theaterId, date=$date');

      List<ShowtimeModel> showtimeList;
      if (date != null && date.isNotEmpty) {
        
        print('Loading showtimes with date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieTheaterAndDate(
            movieId, theaterId, date);
      } else {
        
        print('Loading showtimes without date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieAndTheater(
            movieId, theaterId);
      }

      
      final bookableShowtimes = showtimeList.where((showtime) {
        return showtime.isBookable;
      }).toList();

      print(
          'Found ${showtimeList.length} total showtimes, ${bookableShowtimes.length} bookable');
      showtimes.value = bookableShowtimes;
    } catch (e) {
      print('Error loading showtimes: $e');
      errorMessage.value = 'Không thể tải lịch chiếu: $e';
      showtimes.clear();
    } finally {
      isLoading.value = false;
    }
  }

  
  Future<void> loadScreen(String screenId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final screen = await _screenService.getScreenById(screenId);
      selectedScreen.value = screen;
    } catch (e) {
      errorMessage.value = 'Không thể tải thông tin phòng chiếu: $e';
      selectedScreen.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  

  
  void setSelectedMovie(Movie movie) {
    selectedMovie.value = movie;
    
    
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
  }

  
  
  void setSelectedTheater(TheaterModel theater, {String? selectedDate}) {
    selectedTheater.value = theater;
    
    
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = ''; // Clear previous errors / Xóa lỗi trước đó

    print('setSelectedTheater: ${theater.name}, selectedDate: $selectedDate');

    
    
    if (selectedMovie.value != null) {
      loadShowtimes(selectedMovie.value!.id, theater.id, date: selectedDate);
    }
  }

  
  
  void setSelectedShowtime(ShowtimeModel showtime) {
    selectedShowtime.value = showtime;
    
    
    selectedSeats.clear();
    totalPrice.value = 0.0;

    
    
    loadScreen(showtime.screenId);
  }

  
  

  
  
  void toggleSeat(String seatId) {
    if (selectedSeats.contains(seatId)) {
      selectedSeats.remove(seatId);
    } else {
      selectedSeats.add(seatId);
    }
    calculateTotalPrice(); 
  }

  
  
  void calculateTotalPrice() {
    if (selectedShowtime.value == null || selectedSeats.isEmpty) {
      totalPrice.value = 0.0;
      return;
    }

    double total = 0.0;
    final showtime = selectedShowtime.value!;

    for (String seatId in selectedSeats) {
      
      
      String seatType = 'standard';
      if (seatId.startsWith('V')) {
        seatType = 'vip';
      } else if (seatId.startsWith('P')) {
        seatType = 'premium';
      }

      total += showtime.pricing.getPriceForSeatType(seatType);
    }

    totalPrice.value = total;
  }

  
  
  bool isSeatAvailable(String seatId) {
    if (selectedShowtime.value == null) return false;

    final showtime = selectedShowtime.value!;
    return !showtime.bookedSeats.contains(seatId) &&
        !showtime.reservedSeats.contains(seatId);
  }

  
  
  bool isSeatSelected(String seatId) {
    return selectedSeats.contains(seatId);
  }

  
  

  
  
  Map<String, dynamic> getBookingSummary() {
    return {
      'movie': selectedMovie.value,
      'theater': selectedTheater.value,
      'showtime': selectedShowtime.value,
      'screen': selectedScreen.value,
      'seats': selectedSeats.toList(),
      'totalPrice': totalPrice.value,
      'seatCount': selectedSeats.length,
    };
  }

  
  
  bool isBookingValid() {
    return selectedMovie.value != null &&
        selectedTheater.value != null &&
        selectedShowtime.value != null &&
        selectedScreen.value != null &&
        selectedSeats.isNotEmpty;
  }

  
  
  void clearBooking() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = '';
  }

  
  

  
  
  List<String> getAvailableDates() {
    if (showtimes.isEmpty) return [];

    final dates = showtimes.map((showtime) => showtime.date).toSet().toList();
    dates.sort();
    return dates;
  }

  
  
  List<ShowtimeModel> getShowtimesForDate(String date) {
    return showtimes.where((showtime) => showtime.date == date).toList();
  }

  
  
  String formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )} VNĐ';
  }

  
  
  String getSeatTypeDisplayName(String seatType) {
    switch (seatType) {
      case 'vip':
        return 'VIP';
      case 'premium':
        return 'Premium';
      case 'standard':
        return 'Thường';
      default:
        return 'Thường';
    }
  }
}

# BÁO CÁO CHUYÊN ĐỀ TỐT NGHIỆP
## ỨNG DỤNG ĐẶT VÉ XEM PHIM "ĐỚP PHIM"

---

**TRƯỜNG ĐẠI HỌC [TÊN TRƯỜNG]**  
**KHOA CÔNG NGHỆ THÔNG TIN**

---

**BÁO CÁO CHUYÊN ĐỀ TỐT NGHIỆP**

# ỨNG DỤNG ĐẶT VÉ XEM PHIM "ĐỚP PHIM"
## Sử dụng Flutter và Firebase

---

**Sinh viên thực hiện:** [Tên sinh viên]  
**Mã số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Khóa:** [Khóa học]

**Giảng viên hướng dẫn:** [Tên giảng viên]  
**Chức danh:** [Chức danh]

---

**Thành phố, năm 2024**

---

## LỜI CAM ĐOAN

Tôi xin cam đoan rằng báo cáo chuyên đề tốt nghiệp với đề tài "Ứng dụng đặt vé xem phim 'Đớp Phim' sử dụng Flutter và Firebase" là công trình nghiên cứu của riêng tôi dưới sự hướng dẫn của giảng viên [Tên giảng viên].

Các kết quả nghiên cứu và kết luận trong báo cáo này là trung thực và không sao chép từ bất kỳ nguồn nào khác dưới mọi hình thức. Tôi xin hoàn toàn chịu trách nhiệm về tính chính xác và tính trung thực của nội dung báo cáo này.

Tôi xin cảm ơn!

**Thành phố, ngày ... tháng ... năm 2024**

**Sinh viên thực hiện**

[Chữ ký]

[Tên sinh viên]

---

## LỜI CẢM ƠN

Trước tiên, tôi xin bày tỏ lòng biết ơn sâu sắc đến giảng viên hướng dẫn [Tên giảng viên], người đã tận tình hướng dẫn, chỉ bảo và đưa ra những góp ý quý báu trong suốt quá trình thực hiện đề tài này.

Tôi xin chân thành cảm ơn các thầy cô trong Khoa Công nghệ Thông tin, Trường Đại học [Tên trường] đã truyền đạt kiến thức và tạo điều kiện thuận lợi để tôi hoàn thành chương trình học.

Tôi cũng xin gửi lời cảm ơn đến gia đình, bạn bè đã luôn động viên, ủng hộ và tạo điều kiện tốt nhất để tôi có thể tập trung hoàn thành đề tài này.

Cuối cùng, tôi xin cảm ơn những người dùng đã tham gia khảo sát và thử nghiệm ứng dụng, góp phần quan trọng vào việc hoàn thiện sản phẩm.

Mặc dù đã rất cố gắng, nhưng báo cáo này chắc chắn không tránh khỏi những thiếu sót. Tôi rất mong nhận được sự góp ý từ các thầy cô và bạn đọc để báo cáo được hoàn thiện hơn.

**Sinh viên thực hiện**

[Tên sinh viên]

---

## MỤC LỤC

**LỜI MỞ ĐẦU** .......................................................... 1  

**1. GIỚI THIỆU ĐỀ TÀI** ................................................ 2  
1.1. Tổng quan về đề tài ................................................ 2  
1.2. Bối cảnh và động lực phát triển .................................... 2  
1.2.1. Bối cảnh thị trường .............................................. 2  
1.2.2. Thói quen người tiêu dùng và sự cần thiết của ứng dụng ........... 3  
1.2.3. Cơ hội phát triển ................................................ 4  
1.3. Mục tiêu đề tài .................................................... 4  
1.3.1. Mục tiêu ngắn hạn (3-12 tháng) ................................... 4  
1.3.2. Mục tiêu trung hạn (1-2 năm) ..................................... 4  
1.3.3. Mục tiêu dài hạn (3-5 năm) ....................................... 4  
1.4. Đối tượng người dùng mục tiêu ...................................... 5  
1.4.1. Phân khúc người dùng chính ....................................... 5  
1.4.2. Phân khúc người dùng thứ cấp ..................................... 5  
1.5. Phạm vi và giới hạn đề tài .......................................... 5  
1.5.1. Chức năng được bao gồm ........................................... 5  
1.5.2. Nền tảng được hỗ trợ ............................................. 6  
1.5.3. Giới hạn đề tài .................................................. 6  
1.6. Lợi ích mong đợi ................................................... 6  
1.6.1. Lợi ích cho người dùng ........................................... 6  
1.6.2. Lợi ích cho rạp chiếu ............................................ 7  
1.6.3. Lợi ích cho ngành điện ảnh ....................................... 7  
1.7. Yếu tố thành công quan trọng ........................................ 7  
1.7.1. Yếu tố kỹ thuật .................................................. 7  
1.7.2. Yếu tố kinh doanh ................................................ 7  
1.7.3. Yếu tố người dùng ................................................ 8  

**2. PHÂN TÍCH YÊU CẦU** ................................................. 8  
2.1. Tổng quan về yêu cầu ............................................... 8  
2.1.1. Phương pháp thu thập yêu cầu ..................................... 8  
2.1.2. Phân loại yêu cầu ................................................ 8  
2.2. Yêu cầu chức năng chi tiết ......................................... 9  
2.2.1. Module Xác thực và Quản lý người dùng ........................... 9  
2.2.2. Module Khám phá và Tìm kiếm Phim ................................ 15  
2.2.3. Module Đặt vé và Chọn ghế ....................................... 21  
2.2.4. Module Thanh toán ................................................ 23  
2.2.5. Module Quản lý Vé ................................................ 23  
2.2.6. Module Thông báo ................................................. 24  
2.2.7. Module Admin ..................................................... 25  
2.3. Yêu cầu phi chức năng .............................................. 26  
2.3.1. Hiệu suất (Performance) .......................................... 26  
2.3.2. Bảo mật (Security) ............................................... 27  
2.3.3. Khả năng sử dụng (Usability) ..................................... 27  
2.3.4. Khả năng bảo trì (Maintainability) ............................... 28  
2.3.5. Khả năng mở rộng (Scalability) ................................... 28  
2.4. Yêu cầu ràng buộc .................................................. 29  
2.4.1. Công nghệ ........................................................ 29  
2.4.2. Nền tảng ......................................................... 29  
2.4.3. Tuân thủ ......................................................... 29  
2.5. Use Cases chính .................................................... 29  
2.5.1. Tổng quan Use Cases .............................................. 29  
2.5.2. Use Case chi tiết ................................................ 31  
2.6. User Stories ....................................................... 33  
2.6.1. Epic: Đặt vé xem phim ............................................ 33  
2.6.2. Epic: Quản lý tài khoản .......................................... 34  
2.6.3. Epic: Thanh toán ................................................. 34  
2.6.4. Epic: Quản trị hệ thống .......................................... 34  
2.7. Acceptance Criteria tổng quát ...................................... 35  
2.7.1. Tiêu chí chất lượng .............................................. 35  
2.7.2. Tiêu chí kinh doanh .............................................. 35  

**3. LỰA CHỌN CÔNG NGHỆ** ............................................... 35  
3.1. Tổng quan về quyết định công nghệ .................................. 35  
3.1.1. Tiêu chí lựa chọn công nghệ ...................................... 36  
3.2. Lý do chọn Flutter Framework ....................................... 36  
3.2.1. Ưu điểm vượt trội của Flutter .................................... 36  
3.2.2. So sánh với các lựa chọn khác .................................... 37  
3.2.3. Flutter trong bối cảnh đề tài .................................... 38  
3.2.4. Phiên bản và cấu hình Flutter .................................... 39  
3.3. Firebase Ecosystem ................................................. 39  
3.3.1. Tổng quan Firebase ............................................... 39  
3.3.2. Firebase Services được sử dụng ................................... 39  
3.3.3. Firebase Configuration ........................................... 43  
3.3.4. Lợi ích của Firebase Ecosystem ................................... 44  
3.4. Các thư viện và dependencies chính ................................. 45  
3.4.1. State Management - GetX .......................................... 45  
3.4.2. UI và Design System .............................................. 46  
3.4.3. Media và Content ................................................. 47  
3.4.4. Network và Data .................................................. 49  
3.4.5. Utilities và Tools ............................................... 49  
3.4.6. Data Processing .................................................. 51  
3.4.7. Payment Integration .............................................. 51  
3.4.8. Development và Testing ........................................... 52  
3.4.9. Dependencies Summary ............................................. 52  

---

## LỜI MỞ ĐẦU

Ngành công nghiệp điện ảnh Việt Nam đang trải qua giai đoạn phát triển mạnh mẽ với doanh thu phòng vé năm 2023 đạt hơn 3.200 tỷ VNĐ và hơn 52 triệu lượt khán giả. Tuy nhiên, việc ứng dụng công nghệ thông tin trong ngành này vẫn còn nhiều hạn chế, đặc biệt là trong việc đặt vé trực tuyến.

Chuyên đề tốt nghiệp này trình bày quá trình nghiên cứu, thiết kế và triển khai ứng dụng đặt vé xem phim "Đớp Phim" - một giải pháp toàn diện sử dụng công nghệ Flutter và Firebase. Ứng dụng được phát triển nhằm giải quyết các vấn đề thực tế trong việc đặt vé xem phim tại Việt Nam, mang lại trải nghiệm tốt hơn cho người dùng và hiệu quả cao hơn cho các rạp chiếu phim.

Báo cáo này gồm 3 phần chính: Giới thiệu đề tài, Phân tích yêu cầu, và Lựa chọn công nghệ, cung cấp cái nhìn toàn diện về quá trình phát triển một ứng dụng đặt vé hiện đại và hiệu quả.

---

## 1. GIỚI THIỆU ĐỀ TÀI

### 1.1. Tổng quan về đề tài

Đề tài "Ứng dụng đặt vé xem phim 'Đớp Phim'" là một dự án phát triển ứng dụng mobile và web toàn diện cho việc đặt vé xem phim trực tuyến. Ứng dụng được xây dựng trên nền tảng Flutter framework kết hợp với Firebase ecosystem, cho phép người dùng dễ dàng tìm kiếm phim, chọn rạp, đặt vé và thanh toán một cách thuận tiện và an toàn.

Tên ứng dụng "Đớp Phim" thể hiện sự nhanh chóng, tiện lợi trong việc "đớp" lấy những suất chiếu yêu thích, phản ánh đúng mục tiêu của ứng dụng là mang đến trải nghiệm đặt vé nhanh chóng và hiệu quả cho người dùng Việt Nam.

### 1.2. Bối cảnh và động lực phát triển

#### 1.2.1. Bối cảnh thị trường

Ngành công nghiệp điện ảnh Việt Nam đang trải qua giai đoạn phát triển mạnh mẽ và bền vững. Theo báo cáo của Cục Điện ảnh - Bộ Văn hóa, Thể thao và Du lịch:

- **Doanh thu phòng vé 2023:** Hơn 3.200 tỷ VNĐ, tăng 15% so với năm 2022
- **Lượt khán giả:** Hơn 52 triệu lượt người xem phim tại rạp
- **Hạ tầng rạp chiếu:** 1.200 rạp với hơn 180.000 ghế trên toàn quốc
- **Phân bố địa lý:** Tập trung chủ yếu tại Hà Nội, TP.HCM, Đà Nẵng và các tỉnh thành phát triển

**Thách thức hiện tại:**
- Phần lớn rạp chiếu, đặc biệt là rạp độc lập, vẫn sử dụng phương thức bán vé truyền thống
- Thiếu nền tảng tổng hợp cho phép đặt vé từ nhiều rạp khác nhau
- Trải nghiệm người dùng chưa tối ưu với quy trình đặt vé phức tạp
- Hạn chế trong tích hợp công nghệ và dịch vụ thanh toán

#### 1.2.2. Thói quen người tiêu dùng và sự cần thiết của ứng dụng

Theo khảo sát của Nielsen Vietnam (2023) về hành vi tiêu dùng giải trí:

**Xu hướng số hóa:**
- 78% khán giả sử dụng smartphone để tìm hiểu thông tin phim
- 65% mong muốn đặt vé trực tuyến thay vì đến quầy vé
- 82% quan tâm đến việc chọn ghế trước khi đến rạp
- 71% sẵn sàng thanh toán điện tử cho vé xem phim

**Điểm đau của người dùng:**
- **Thời gian chờ đợi:** Xếp hàng tại quầy vé, đặc biệt cuối tuần và lễ tết
- **Thông tin hạn chế:** Khó so sánh lịch chiếu, giá vé giữa các rạp
- **Trải nghiệm không tối ưu:** Không biết trước tình trạng ghế trống
- **Thanh toán bất tiện:** Chỉ chấp nhận tiền mặt tại nhiều rạp

**Nhu cầu thực tế:**
- Ứng dụng đặt vé đa rạp với giao diện thân thiện
- Tính năng chọn ghế real-time và thanh toán đa dạng
- Thông tin phim phong phú với trailer và đánh giá
- Hệ thống thông báo và quản lý vé điện tử

#### 1.2.3. Cơ hội phát triển

**Thị trường tiềm năng:**
- Tỷ lệ smartphone penetration: 84% dân số Việt Nam (2023)
- Tăng trưởng thương mại điện tử: 25% năm/năm
- Thói quen thanh toán số: 68% người dùng sử dụng ví điện tử

**Cơ hội công nghệ:**
- Flutter cho phép phát triển cross-platform hiệu quả
- Firebase cung cấp backend-as-a-service scalable
- AI/ML integration cho recommendation system
- Real-time technology cho seat booking

**Lợi thế cạnh tranh:**
- Ứng dụng đa rạp đầu tiên tại Việt Nam
- Tích hợp sâu với hệ sinh thái thanh toán Việt Nam
- UI/UX được thiết kế riêng cho người dùng Việt
- Chi phí phát triển thấp nhờ cross-platform

### 1.3. Mục tiêu đề tài

#### 1.3.1. Mục tiêu ngắn hạn (3-12 tháng)

**Phát triển MVP (Minimum Viable Product):**
- Hoàn thành ứng dụng với đầy đủ tính năng cơ bản
- Tích hợp 5-10 rạp chiếu phim tại TP.HCM
- Đạt 1.000 người dùng đăng ký trong 3 tháng đầu
- Xử lý 500 giao dịch đặt vé/tháng

**Tính năng cốt lõi:**
- Đăng ký/đăng nhập với Firebase Authentication
- Tìm kiếm và xem thông tin phim từ TMDB API
- Đặt vé với quy trình 5 bước đơn giản
- Thanh toán PayPal và chọn ghế real-time
- Admin panel cơ bản cho quản lý rạp

#### 1.3.2. Mục tiêu trung hạn (1-2 năm)

**Mở rộng thị trường:**
- Tích hợp 50+ rạp chiếu trên toàn quốc
- Đạt 10.000 người dùng hoạt động hàng tháng
- Xử lý 5.000 giao dịch/tháng
- Mở rộng ra Hà Nội và Đà Nẵng

**Tính năng nâng cao:**
- Tích hợp VNPay, MoMo cho thanh toán
- Hệ thống recommendation AI
- Loyalty program và điểm thưởng
- Social features (review, rating, sharing)
- Food & beverage ordering

#### 1.3.3. Mục tiêu dài hạn (3-5 năm)

**Trở thành nền tảng hàng đầu:**
- Market leader trong đặt vé trực tuyến tại Việt Nam
- 100.000+ người dùng hoạt động
- Tích hợp 200+ rạp chiếu toàn quốc
- Mở rộng sang Đông Nam Á

**Đa dạng hóa dịch vụ:**
- Platform cho events, concerts, sports
- White-label solution cho cinema chains
- B2B services cho theater management
- Blockchain-based ticket verification

### 1.4. Đối tượng người dùng mục tiêu

#### 1.4.1. Phân khúc người dùng chính

**Người dùng cá nhân (Primary Users)**
- **Độ tuổi:** 18-35 tuổi
- **Thu nhập:** Trung bình trở lên (>8 triệu VNĐ/tháng)
- **Địa lý:** Thành phố lớn (TP.HCM, Hà Nội, Đà Nẵng)
- **Hành vi:** Xem phim 2-4 lần/tháng, tech-savvy, sử dụng smartphone thường xuyên

**Gia đình có con (Family Users)**
- **Độ tuổi:** 25-45 tuổi
- **Đặc điểm:** Có con từ 6-16 tuổi, quan tâm đến phim gia đình
- **Nhu cầu:** Đặt vé nhóm, chọn ghế gần nhau, thông tin phim phù hợp lứa tuổi

**Nhóm bạn trẻ (Young Groups)**
- **Độ tuổi:** 16-25 tuổi
- **Đặc điểm:** Học sinh, sinh viên, nhân viên trẻ
- **Nhu cầu:** Đặt vé nhóm, chia sẻ chi phí, phim trending

#### 1.4.2. Phân khúc người dùng thứ cấp

**Quản trị viên rạp chiếu (Cinema Admins)**
- **Vai trò:** Quản lý lịch chiếu, giá vé, báo cáo doanh thu
- **Nhu cầu:** Dashboard analytics, quản lý inventory, customer insights

**Nhân viên rạp chiếu (Cinema Staff)**
- **Vai trò:** Xử lý đặt vé, check-in khách hàng
- **Nhu cầu:** Mobile app cho staff, QR code scanning, customer support

### 1.5. Phạm vi và giới hạn đề tài

#### 1.5.1. Chức năng được bao gồm:

**Core Features:**
- Đăng ký/đăng nhập (email, Google Sign-In)
- Tìm kiếm và lọc phim theo thể loại, rạp, thời gian
- Xem chi tiết phim (synopsis, cast, trailer, rating)
- Đặt vé với quy trình 5 bước
- Chọn ghế real-time với conflict resolution
- Thanh toán PayPal an toàn
- Quản lý vé điện tử với QR code
- Thông báo push và in-app
- Admin panel cho quản lý rạp/lịch chiếu

**Advanced Features:**
- Multi-language support (Tiếng Việt, English)
- Offline mode với cached data
- Dark/Light theme
- Accessibility support
- Analytics và crash reporting

#### 1.5.2. Nền tảng được hỗ trợ

**Mobile Platforms:**
- Android 5.0+ (API Level 21+)
- iOS 11.0+
- Responsive design cho tablet

**Web Platform:**
- Progressive Web App (PWA)
- Modern browsers: Chrome 90+, Safari 14+, Firefox 88+
- Desktop và mobile web support

**Backend Infrastructure:**
- Firebase ecosystem (Authentication, Firestore, Functions, Storage)
- Cloud-based với auto-scaling
- Global CDN cho performance tối ưu

#### 1.5.3. Giới hạn đề tài

**Chức năng không bao gồm:**
- Hệ thống POS tại rạp chiếu
- Quản lý F&B (đồ ăn, nước uống)
- Tích hợp hệ thống kế toán/ERP
- Live streaming phim
- Cryptocurrency payment

**Giới hạn kỹ thuật:**
- Chỉ hỗ trợ PayPal cho thanh toán (giai đoạn đầu)
- Không hỗ trợ offline booking
- Giới hạn 8 ghế/giao dịch
- Không hỗ trợ group booking phức tạp

**Giới hạn địa lý:**
- Tập trung thị trường Việt Nam
- Tiếng Việt và English only
- Múi giờ GMT+7

### 1.6. Lợi ích mong đợi

#### 1.6.1. Lợi ích cho người dùng

**Tiện lợi và tiết kiệm thời gian:**
- Đặt vé 24/7 từ bất kỳ đâu
- Không cần xếp hàng tại quầy vé
- So sánh giá và lịch chiếu nhiều rạp
- Lưu trữ vé điện tử an toàn

**Trải nghiệm tốt hơn:**
- Chọn ghế trước khi đến rạp
- Thông tin phim phong phú với trailer
- Thông báo lịch chiếu và khuyến mãi
- Giao diện thân thiện, dễ sử dụng

**Tính năng thông minh:**
- Recommendation phim phù hợp
- Lịch sử đặt vé và thống kê cá nhân
- Chia sẻ với bạn bè dễ dàng
- Hỗ trợ đa ngôn ngữ

#### 1.6.2. Lợi ích cho rạp chiếu

**Tăng doanh thu:**
- Tiếp cận khách hàng rộng hơn
- Bán vé online 24/7
- Giảm chi phí nhân sự quầy vé
- Tăng tỷ lệ lấp đầy ghế

**Quản lý hiệu quả:**
- Dashboard analytics real-time
- Dự đoán nhu cầu chính xác
- Quản lý inventory tự động
- Báo cáo doanh thu chi tiết

**Marketing và CRM:**
- Database khách hàng phong phú
- Targeted marketing campaigns
- Loyalty program tích hợp
- Customer insights sâu sắc

#### 1.6.3. Lợi ích cho ngành điện ảnh

**Chuyển đổi số:**
- Thúc đẩy digitalization trong ngành
- Nâng cao trải nghiệm khách hàng
- Tăng hiệu quả vận hành
- Giảm chi phí transaction

**Phát triển thị trường:**
- Mở rộng tiếp cận khán giả
- Hỗ trợ rạp độc lập cạnh tranh
- Tăng tần suất xem phim
- Phát triển văn hóa xem phim

**Dữ liệu và insights:**
- Market intelligence toàn ngành
- Xu hướng tiêu dùng real-time
- Hỗ trợ quyết định kinh doanh
- Benchmark performance

### 1.7. Yếu tố thành công quan trọng

#### 1.7.1. Yếu tố kỹ thuật

**Performance và Reliability:**
- Thời gian phản hồi < 1 giây
- Uptime 99.5%+
- Scalability cho 10,000+ concurrent users
- Real-time seat booking không conflict

**Security và Compliance:**
- PCI DSS compliance cho payment
- GDPR compliance cho data protection
- End-to-end encryption
- Secure authentication và authorization

**User Experience:**
- Intuitive UI/UX design
- Cross-platform consistency
- Accessibility support
- Offline capability

#### 1.7.2. Yếu tố kinh doanh

**Partnership Strategy:**
- Onboard key cinema chains
- Competitive commission structure
- Value-added services
- Long-term contracts

**Market Penetration:**
- Aggressive marketing campaigns
- Influencer partnerships
- Referral programs
- Competitive pricing

**Revenue Model:**
- Commission từ ticket sales (5-8%)
- Premium features cho cinemas
- Advertising revenue
- Data analytics services

#### 1.7.3. Yếu tố người dùng

**User Acquisition:**
- App Store Optimization (ASO)
- Social media marketing
- Word-of-mouth referrals
- Partnership với cinemas

**User Retention:**
- Loyalty program hiệu quả
- Personalized recommendations
- Regular feature updates
- Excellent customer support

**Community Building:**
- Social features (reviews, ratings)
- Movie discussion forums
- Events và contests
- User-generated content

---

## 2. PHÂN TÍCH YÊU CẦU

### 2.1. Tổng quan về yêu cầu

#### 2.1.1. Phương pháp thu thập yêu cầu

**Nghiên cứu thị trường:**
- Khảo sát 200 người dùng về thói quen xem phim
- Phỏng vấn 15 quản lý rạp chiếu
- Phân tích 5 ứng dụng đặt vé hiện có
- Nghiên cứu best practices quốc tế

**Stakeholder Analysis:**
- End users (khách hàng xem phim)
- Cinema operators (quản lý rạp)
- Cinema staff (nhân viên rạp)
- System administrators
- Business stakeholders

**Requirements Gathering Techniques:**
- User interviews và focus groups
- Surveys và questionnaires
- Competitive analysis
- User journey mapping
- Persona development

#### 2.1.2. Phân loại yêu cầu

**Functional Requirements (FR):**
- Các chức năng cụ thể mà hệ thống phải thực hiện
- User interactions và system behaviors
- Business logic và workflows
- Data processing requirements

**Non-Functional Requirements (NFR):**
- Performance, security, usability
- Scalability, reliability, maintainability
- Compliance và regulatory requirements
- Technical constraints

**Constraint Requirements:**
- Technology stack limitations
- Budget và timeline constraints
- Regulatory compliance
- Integration requirements

### 2.2. Yêu cầu chức năng chi tiết

#### 2.2.1. Module Xác thực và Quản lý người dùng

**UC-001: Đăng ký tài khoản**

*Mô tả:* Người dùng tạo tài khoản mới để sử dụng ứng dụng

*Actors:* Người dùng chưa có tài khoản

*Preconditions:*
- Ứng dụng đã được cài đặt
- Có kết nối internet

*Main Success Scenario:*
1. Người dùng mở ứng dụng lần đầu
2. Chọn "Đăng ký tài khoản mới"
3. Nhập thông tin: email, mật khẩu, họ tên, số điện thoại
4. Xác nhận điều khoản sử dụng
5. Hệ thống gửi email xác thực
6. Người dùng click link xác thực trong email
7. Tài khoản được kích hoạt thành công

*Alternative Flows:*
- 3a. Email đã tồn tại → Hiển thị lỗi, đề xuất đăng nhập
- 3b. Mật khẩu không đủ mạnh → Hiển thị yêu cầu mật khẩu
- 5a. Gửi email thất bại → Retry hoặc đăng ký bằng phone
- 6a. Link xác thực hết hạn → Gửi lại email mới

*Postconditions:*
- Tài khoản được tạo và xác thực
- Người dùng có thể đăng nhập
- Profile cơ bản được khởi tạo

*Business Rules:*
- Email phải unique trong hệ thống
- Mật khẩu tối thiểu 8 ký tự, có chữ hoa, số, ký tự đặc biệt
- Xác thực email bắt buộc trước khi sử dụng
- Auto-login sau khi đăng ký thành công

**UC-002: Đăng nhập**

*Mô tả:* Người dùng đã có tài khoản đăng nhập vào ứng dụng

*Actors:* Người dùng đã đăng ký

*Preconditions:*
- Tài khoản đã được tạo và xác thực
- Ứng dụng đã được cài đặt
- Có kết nối internet

*Main Success Scenario:*
1. Người dùng mở ứng dụng
2. Nhập email và mật khẩu
3. Chọn "Đăng nhập"
4. Hệ thống xác thực thông tin
5. Chuyển đến màn hình chính
6. Lưu session cho lần đăng nhập tiếp theo

*Alternative Flows:*
- 2a. Chọn "Đăng nhập bằng Google" → OAuth flow
- 4a. Thông tin không chính xác → Hiển thị lỗi
- 4b. Tài khoản bị khóa → Hiển thị thông báo liên hệ support
- 4c. Quên mật khẩu → Chuyển đến flow reset password

*Postconditions:*
- Người dùng được xác thực và đăng nhập
- Session được lưu trữ
- Access token được tạo

**UC-003: Quản lý hồ sơ**

*Mô tả:* Người dùng xem và cập nhật thông tin cá nhân

**3.1 Thông tin cơ bản:**
- Họ tên, email, số điện thoại
- Ngày sinh, giới tính
- Địa chỉ (tùy chọn)
- Ngôn ngữ ưa thích

**3.2 Ảnh đại diện:**
- Upload từ gallery hoặc camera
- Crop và resize tự động
- Lưu trữ trên Firebase Storage
- Fallback avatar mặc định

**3.3 Bảo mật:**
- Đổi mật khẩu
- Bật/tắt 2FA (future)
- Quản lý sessions
- Xóa tài khoản

**3.4 Preferences:**
- Thể loại phim yêu thích
- Rạp chiếu ưa thích
- Cài đặt thông báo
- Theme (dark/light)

**3.5 Lịch sử hoạt động:**
- Lịch sử đặt vé
- Phim đã xem
- Reviews đã viết
- Điểm thưởng tích lũy

**UC-004: Phân quyền người dùng**

*Mô tả:* Hệ thống phân quyền 3 cấp độ với các tính năng khác nhau

**4.1 User (Người dùng thường):**

*Quyền truy cập:*
- Xem danh sách phim và thông tin chi tiết
- Tìm kiếm và lọc phim
- Đặt vé và thanh toán
- Quản lý vé đã đặt
- Xem và chỉnh sửa profile
- Nhận thông báo

*Giới hạn:*
- Không thể truy cập admin functions
- Không thể xem dữ liệu của users khác
- Giới hạn 8 vé/giao dịch
- Không thể cancel vé sau 2 giờ

*UI Elements:*
- Bottom navigation: Home, Search, Tickets, Profile
- Standard movie listing và detail screens
- Basic booking flow
- Personal ticket history

**4.2 Admin (Quản trị viên):**

*Cách kích hoạt:* Tap 7 lần liên tiếp vào username trong profile

*Quyền bổ sung:*
- Quản lý rạp chiếu (thêm, sửa, xóa)
- Quản lý phòng chiếu và seat layout
- Tạo và quản lý lịch chiếu
- Xem báo cáo doanh thu và analytics
- Quản lý users (view, suspend)
- Bulk import movies và theaters

*Admin Dashboard:*
- Theater management interface
- Schedule management với calendar view
- Revenue reports và analytics
- User management tools
- System health monitoring

*Workflow quản lý lịch chiếu:*
1. Chọn rạp và phòng chiếu
2. Chọn phim từ database
3. Set thời gian chiếu và giá vé
4. Configure seat pricing (VIP, standard)
5. Publish schedule

**4.3 Developer (Nhà phát triển):**

*Quyền đặc biệt:*
- Tất cả quyền của Admin
- Access debug information
- View system logs và error reports
- Database direct access (read-only)
- Performance monitoring tools
- Feature flags management

*Developer Tools:*
- Debug console trong app
- Network request inspector
- Database query analyzer
- Crash report viewer
- A/B testing controls

*Security:*
- Developer mode chỉ available trong debug builds
- Production builds không có developer tools
- Audit log cho tất cả developer actions
- Time-limited developer sessions

### 2.2.2. Module Khám phá và Tìm kiếm Phim

**UC-005: Duyệt danh sách phim**

*Mô tả:* Người dùng xem danh sách phim có sẵn với nhiều cách sắp xếp và lọc

**5.1 Danh mục phim:**

*Now Playing (Đang chiếu):*
- Phim hiện đang có lịch chiếu
- Sắp xếp theo popularity hoặc release date
- Show showtimes available today
- Quick book button

*Coming Soon (Sắp chiếu):*
- Phim sẽ ra mắt trong 30 ngày tới
- Pre-booking available (nếu có)
- Notification reminder option
- Trailer preview

*Popular (Phổ biến):*
- Top movies theo TMDB popularity
- User ratings và reviews
- Trending indicators
- Social sharing options

*By Genre (Theo thể loại):*
- Action, Comedy, Drama, Horror, etc.
- Vietnamese genre names với English mapping
- Genre-specific filtering
- Personalized recommendations

**5.2 Tính năng lọc và sắp xếp:**

*Filter Options:*
- Thể loại (multiple selection)
- Rạp chiếu (location-based)
- Thời gian chiếu (today, tomorrow, this week)
- Giá vé (price range)
- Rating (TMDB score, user reviews)
- Ngôn ngữ (Vietnamese, English, etc.)

*Sort Options:*
- Popularity (mặc định)
- Release date (newest first)
- Rating (highest first)
- Alphabetical (A-Z)
- Price (lowest first)
- Distance (nearest theaters)

**5.3 Hiển thị thông tin:**

*Movie Card Layout:*
- Poster image (2:3 aspect ratio)
- Title (Vietnamese + Original)
- Genre tags
- Rating stars và score
- Duration
- Quick action buttons (Trailer, Book)

*List vs Grid View:*
- Grid: 2 columns trên mobile, 3-4 trên tablet
- List: Detailed info với horizontal layout
- User preference saved
- Smooth transition animation

**5.4 Performance & UX:**

*Lazy Loading:*
- Load 20 movies initially
- Infinite scroll với pagination
- Skeleton loading animation
- Error handling với retry option

*Caching Strategy:*
- Cache movie posters locally
- Offline mode với cached data
- Smart refresh khi có network
- Background sync

**UC-006: Tìm kiếm phim**

*Mô tả:* Người dùng tìm kiếm phim theo nhiều tiêu chí khác nhau
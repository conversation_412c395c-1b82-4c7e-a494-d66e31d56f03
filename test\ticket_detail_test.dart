import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:dop_phim/models/ticket_model.dart';
import 'package:dop_phim/view/page/ticket_detail_page.dart';

void main() {
  group('TicketDetailPage Tests', () {
    late Ticket testTicket;

    setUp(() {
      
      testTicket = Ticket(
        id: 'test_ticket_001',
        userId: 'test_user',
        movieId: 123,
        movieTitle: 'Test Movie',
        moviePosterPath: '/test_poster.jpg',
        theaterId: 'theater_001',
        theaterName: 'Test Theater',
        screenId: 'screen_001',
        screenName: 'Screen 1',
        showtimeId: 'showtime_001',
        date: '2024-01-15',
        time: '19:30',
        seats: [
          TicketSeat(row: 'A', number: '1', type: 'standard', price: 100000),
          TicketSeat(row: 'A', number: '2', type: 'standard', price: 100000),
        ],
        totalPrice: 200000,
        discountApplied: 20000,
        finalPrice: 180000,
        paymentMethod: 'paypal',
        bookingCode: 'BOOK123456',
        status: TicketStatus.confirmed,
        purchaseDate: DateTime.now(),
        loyaltyPointsEarned: 18,
      );
    });

    testWidgets('should display ticket details correctly',
        (WidgetTester tester) async {
      
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      
      await tester.pumpAndSettle();

      
      expect(find.text('Test Movie'), findsOneWidget);

      
      expect(find.text('Test Theater'), findsOneWidget);

      
      expect(find.text('Screen 1'), findsOneWidget);

      
      expect(find.text('BOOK123456'), findsOneWidget);

      
      expect(find.text('Đã xác nhận'), findsOneWidget);

      
      expect(find.text('Mã QR Vé'), findsOneWidget);
    });

    testWidgets('should display payment method correctly',
        (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      
      expect(find.text('PayPal'), findsOneWidget);
    });

    testWidgets('should display seat information correctly',
        (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      
      expect(find.text('A1, A2'), findsOneWidget);
    });

    testWidgets('should display price information correctly',
        (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      
      expect(find.textContaining('180.000'), findsOneWidget);
    });

    testWidgets('should handle back navigation', (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TicketDetailPage(ticket: testTicket),
                  ),
                ),
                child: const Text('Go to Detail'),
              ),
            ),
          ),
        ),
      );

      
      await tester.tap(find.text('Go to Detail'));
      await tester.pumpAndSettle();

      
      expect(find.text('Chi Tiết Vé'), findsOneWidget);

      
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      
      expect(find.text('Go to Detail'), findsOneWidget);
    });

    testWidgets('should copy booking code to clipboard',
        (WidgetTester tester) async {
      Get.testMode = true;

      await tester.pumpWidget(
        GetMaterialApp(
          home: TicketDetailPage(ticket: testTicket),
        ),
      );

      await tester.pumpAndSettle();

      
      final copyIcon = find.byIcon(Icons.copy);
      expect(copyIcon, findsOneWidget);

      await tester.tap(copyIcon);
      await tester.pumpAndSettle();

      
      expect(find.text('Đã sao chép'), findsOneWidget);
    });

    test('should generate correct QR data', () {
      
      
      const expectedQRData =
          'TICKET:BOOK123456|MOVIE:Test Movie|DATE:2024-01-15|TIME:19:30|THEATER:Test Theater|SCREEN:Screen 1|SEATS:A1, A2|PRICE:180000.0';

      
      expect(expectedQRData.contains('BOOK123456'), isTrue);
      expect(expectedQRData.contains('Test Movie'), isTrue);
      expect(expectedQRData.contains('2024-01-15'), isTrue);
      expect(expectedQRData.contains('19:30'), isTrue);
    });
  });
}

enum RealtimeBugStatus {
  pending, 
  accepted, 
  inProgress, 
  fixed 
}

extension RealtimeBugStatusExtension on RealtimeBugStatus {
  String get name {
    switch (this) {
      case RealtimeBugStatus.pending:
        return 'pending';
      case RealtimeBugStatus.accepted:
        return 'accepted';
      case RealtimeBugStatus.inProgress:
        return 'inProgress';
      case RealtimeBugStatus.fixed:
        return 'fixed';
    }
  }

  String get displayName {
    switch (this) {
      case RealtimeBugStatus.pending:
        return 'Chưa nhận';
      case RealtimeBugStatus.accepted:
        return 'Đã nhận';
      case RealtimeBugStatus.inProgress:
        return 'Đang fix';
      case RealtimeBugStatus.fixed:
        return 'Đã fix';
    }
  }

  static RealtimeBugStatus fromString(String? value) {
    if (value == 'accepted') return RealtimeBugStatus.accepted;
    if (value == 'inProgress') return RealtimeBugStatus.inProgress;
    if (value == 'fixed') return RealtimeBugStatus.fixed;
    return RealtimeBugStatus.pending; 
  }
}

class RealtimeBugReportModel {
  final String id;
  final String title;
  final String description;
  final String reportedBy; 
  final String reportedByName;
  final String? reportedByEmail;
  final int createdAt;
  final String status;
  final String? assignedTo; 
  final String? assignedToName;
  final List<RealtimeBugResponseModel>? responses;
  final Map<String, dynamic>? additionalData;
  final bool allowUserResponse; 

  RealtimeBugReportModel({
    required this.id,
    required this.title,
    required this.description,
    required this.reportedBy,
    required this.reportedByName,
    this.reportedByEmail,
    required this.createdAt,
    required this.status,
    this.assignedTo,
    this.assignedToName,
    this.responses,
    this.additionalData,
    this.allowUserResponse = true, 
  });

  factory RealtimeBugReportModel.fromJson(
      String id, Map<String, dynamic> json) {
    List<RealtimeBugResponseModel>? responsesList;

    if (json['responses'] != null) {
      final Map<String, dynamic> responsesMap =
          Map<String, dynamic>.from(json['responses']);
      responsesList = responsesMap.entries.map((entry) {
        return RealtimeBugResponseModel.fromJson(
            entry.key, Map<String, dynamic>.from(entry.value));
      }).toList();

      
      responsesList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    }

    return RealtimeBugReportModel(
      id: id,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      reportedBy: json['reportedBy'] ?? '',
      reportedByName: json['reportedByName'] ?? 'Unknown User',
      reportedByEmail: json['reportedByEmail'],
      createdAt: json['createdAt'] ?? 0,
      status: json['status'] ?? 'pending',
      assignedTo: json['assignedTo'],
      assignedToName: json['assignedToName'],
      responses: responsesList,
      additionalData: json['additionalData'],
      allowUserResponse: json['allowUserResponse'] ?? true, // Mặc định cho phép
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> responsesMap = {};
    if (responses != null) {
      for (var response in responses!) {
        responsesMap[response.id] = response.toJson();
      }
    }

    return {
      'title': title,
      'description': description,
      'reportedBy': reportedBy,
      'reportedByName': reportedByName,
      'reportedByEmail': reportedByEmail,
      'createdAt': createdAt,
      'status': status,
      'assignedTo': assignedTo,
      'assignedToName': assignedToName,
      'responses': responsesMap,
      'additionalData': additionalData,
      'allowUserResponse': allowUserResponse,
    };
  }
}

class RealtimeBugResponseModel {
  final String id;
  final String responderId; 
  final String responderName;
  final String message;
  final int createdAt;
  final bool isFromDeveloper;
  final bool isFromAdmin;
  final String? newStatus;

  RealtimeBugResponseModel({
    required this.id,
    required this.responderId,
    required this.responderName,
    required this.message,
    required this.createdAt,
    this.isFromDeveloper = false,
    this.isFromAdmin = false,
    this.newStatus,
  });

  factory RealtimeBugResponseModel.fromJson(
      String id, Map<String, dynamic> json) {
    return RealtimeBugResponseModel(
      id: id,
      responderId: json['responderId'] ?? '',
      responderName: json['responderName'] ?? 'Unknown',
      message: json['message'] ?? '',
      createdAt: json['createdAt'] ?? 0,
      isFromDeveloper: json['isFromDeveloper'] ?? false,
      isFromAdmin: json['isFromAdmin'] ?? false,
      newStatus: json['newStatus'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'responderId': responderId,
      'responderName': responderName,
      'message': message,
      'createdAt': createdAt,
      'isFromDeveloper': isFromDeveloper,
      'isFromAdmin': isFromAdmin,
      'newStatus': newStatus,
    };
  }
}


import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/ticket_model.dart';
import '../controllers/auth_controller.dart';
import '../services/ticket_service.dart';
import '../services/ticket_expiration_service.dart';

class TicketController extends GetxController {
  
  final TicketService _ticketService = TicketService();

  
  final RxList<Ticket> tickets = <Ticket>[].obs; 
  final RxList<Ticket> upcomingTickets = <Ticket>[].obs; 
  final RxList<Ticket> pastTickets = <Ticket>[].obs; 
  final RxBool isLoading = false.obs; 
  final RxString errorMessage = ''.obs; // Thông báo lỗi

  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
    loadTickets(); 

    
    ever(_authController.isLoggedInObs, (_) {
      loadTickets();
    });
  }

  
  Future<void> loadTickets() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      tickets.clear();
      upcomingTickets.clear();
      pastTickets.clear();

      if (_authController.isLoggedIn) {
        
        await _loadFromFirestore();
      } else {
        
        await _loadFromLocalStorage();
      }

      
      _categorizeTickets();
    } catch (e) {
      errorMessage.value = 'Failed to load tickets: $e';
    } finally {
      isLoading.value = false;
    }
  }

  
  Future<void> _loadFromFirestore() async {
    try {
      print('TicketController: Loading tickets from Firestore...');
      final loadedTickets = await _ticketService.getUserTickets();
      print(
          'TicketController: Loaded ${loadedTickets.length} tickets from Firestore');

      
      for (var ticket in loadedTickets) {
        print(
            'TicketController: Ticket - ${ticket.movieTitle} (${ticket.id}) - Date: ${ticket.date} - Status: ${ticket.status.name}');
      }

      tickets.value = loadedTickets;
    } catch (e) {
      print('TicketController: Error loading from Firestore: $e');
      
      await _loadFromLocalStorage();
    }
  }

  
  void _categorizeTickets() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    print('TicketController: Categorizing ${tickets.length} tickets...');
    print('TicketController: Today is: $today');

    final upcoming = <Ticket>[];
    final past = <Ticket>[];

    for (final ticket in tickets) {
      try {
        final ticketDate = DateTime.parse(ticket.date);
        print(
            'TicketController: Ticket ${ticket.movieTitle} - Date: ${ticket.date} -> Parsed: $ticketDate - Status: ${ticket.status.name}');

        
        if (ticket.status != TicketStatus.cancelled) {
          if (ticketDate.isAfter(today) || ticketDate.isAtSameMomentAs(today)) {
            upcoming.add(ticket);
            print('TicketController: -> Added to upcoming');
          } else {
            past.add(ticket);
            print('TicketController: -> Added to past');
          }
        } else {
          print('TicketController: -> Skipped (cancelled)');
        }
      } catch (e) {
        print('TicketController: Error parsing date ${ticket.date}: $e');
        
        
        if (ticket.status != TicketStatus.cancelled) {
          past.add(ticket);
        }
      }
    }

    print(
        'TicketController: Categorization complete - Upcoming: ${upcoming.length}, Past: ${past.length}');

    upcomingTickets.value = upcoming;
    pastTickets.value = past;
  }

  
  
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ticketsJson = prefs.getStringList('tickets') ?? [];

      final List<Ticket> loadedTickets = [];
      for (var json in ticketsJson) {
        final Map<String, dynamic> data = jsonDecode(json);

        
        
        final timestampFields = [
          'purchase_date',
          'purchaseDate',
          'cancelled_at',
          'cancelledAt',
          'used_at',
          'usedAt'
        ];

        for (String field in timestampFields) {
          if (data[field] is String && data[field] != null) {
            try {
              data[field] = Timestamp.fromDate(DateTime.parse(data[field]));
            } catch (e) {
              print('TicketController: Error parsing date field $field: $e');
            }
          }
        }

        loadedTickets.add(Ticket.fromJson(data));
      }

      
      
      loadedTickets.sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));
      tickets.value = loadedTickets;
    } catch (e) {
      print('TicketController: Error loading from local storage: $e');
      errorMessage.value = 'Failed to load tickets from local storage: $e';
    }
  }

  
  
  Future<bool> purchaseTicket(Ticket ticket) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        
        
        final createdTicket = await _ticketService.createTicket(ticket);
        tickets.add(createdTicket);
      } else {
        
        
        tickets.add(ticket);
      }

      
      
      await _saveToLocalStorage();

      
      
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to purchase ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  
  

  
  
  List<Ticket> getUpcomingTickets() {
    return upcomingTickets.toList();
  }

  
  
  List<Ticket> getPastTickets() {
    return pastTickets.toList();
  }

  
  
  Future<void> checkExpiredTickets() async {
    try {
      if (_authController.isLoggedIn) {
        
        
        final expirationService = Get.find<TicketExpirationService>();
        await expirationService.forceCheckExpiredTickets();

        
        
        await loadTickets();
      }
    } catch (e) {
      print('TicketController: Error checking expired tickets: $e');
      errorMessage.value = 'Failed to check expired tickets: $e';
    }
  }

  
  
  Future<bool> cancelTicket(String ticketId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      if (_authController.isLoggedIn) {
        
        
        await _ticketService.cancelTicket(ticketId,
            0.0); 
      }

      
      
      final ticketIndex = tickets.indexWhere((ticket) => ticket.id == ticketId);
      if (ticketIndex != -1) {
        tickets[ticketIndex] = tickets[ticketIndex].copyWith(
          status: TicketStatus.cancelled,
          cancelledAt: DateTime.now(),
        );
      }

      
      
      await _saveToLocalStorage();

      
      
      _categorizeTickets();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to cancel ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  
  
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final List<String> ticketsJson = tickets.map((ticket) {
        final Map<String, dynamic> data = ticket.toJson();

        
        
        final timestampFields = [
          'purchase_date',
          'purchaseDate',
          'cancelled_at',
          'cancelledAt',
          'used_at',
          'usedAt'
        ];

        for (String field in timestampFields) {
          if (data[field] is Timestamp) {
            data[field] = (data[field] as Timestamp).toDate().toIso8601String();
          }
        }

        return jsonEncode(data);
      }).toList();

      await prefs.setStringList('tickets', ticketsJson);
    } catch (e) {
      print('TicketController: Error saving to local storage: $e');
      errorMessage.value = 'Failed to save tickets to local storage: $e';
    }
  }
}

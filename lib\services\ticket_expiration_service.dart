import 'dart:async';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import '../models/ticket_model.dart';
import 'ticket_service.dart';

class TicketExpirationService extends GetxService {
  final TicketService _ticketService = TicketService();
  final FirebaseFunctions _functions = FirebaseFunctions.instance;
  Timer? _expirationTimer;

  
  static const Duration _checkInterval = Duration(minutes: 10);

  @override
  void onInit() {
    super.onInit();
    _startExpirationTimer();
    
    _checkExpiredTickets();
  }

  @override
  void onClose() {
    _expirationTimer?.cancel();
    super.onClose();
  }

  void _startExpirationTimer() {
    _expirationTimer = Timer.periodic(_checkInterval, (timer) {
      _checkExpiredTickets();
    });
  }

  Future<void> _checkExpiredTickets() async {
    try {
      print('Starting ticket expiration check...');
      await _ticketService.checkAndUpdateExpiredTickets();
    } catch (e) {
      print('Error during ticket expiration check: $e');
    }
  }

  
  Future<void> forceCheckExpiredTickets() async {
    await _checkExpiredTickets();
  }

  
  Future<Map<String, dynamic>> updateExpiredTicketsServerSide() async {
    try {
      print('Calling Firebase Function to update expired tickets...');

      final HttpsCallable callable =
          _functions.httpsCallable('manualUpdateExpiredTickets');
      final result = await callable.call();

      print('Firebase Function result: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      print('Error calling Firebase Function: $e');
      throw Exception('Failed to update expired tickets on server: $e');
    }
  }

  
  Future<Map<String, dynamic>> getExpiredTicketsStatsServerSide() async {
    try {
      print('Getting expired tickets stats from server...');

      final HttpsCallable callable =
          _functions.httpsCallable('getExpiredTicketsStats');
      final result = await callable.call();

      print('Server stats result: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      print('Error getting server stats: $e');
      throw Exception('Failed to get server statistics: $e');
    }
  }

  
  Future<Map<String, dynamic>> getExpirationStatus() async {
    try {
      final now = DateTime.now();
      final currentDateString =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      final firestore = FirebaseFirestore.instance;

      
      final confirmedSnapshot = await firestore
          .collection('tickets')
          .where('status', isEqualTo: TicketStatus.confirmed.name)
          .get();

      
      final expiredSnapshot = await firestore
          .collection('tickets')
          .where('status', isEqualTo: TicketStatus.expired.name)
          .get();

      
      final shouldBeExpiredSnapshot = await firestore
          .collection('tickets')
          .where('status', isEqualTo: TicketStatus.confirmed.name)
          .where('date', isLessThan: currentDateString)
          .get();

      return {
        'totalConfirmed': confirmedSnapshot.docs.length,
        'totalExpired': expiredSnapshot.docs.length,
        'shouldBeExpired': shouldBeExpiredSnapshot.docs.length,
        'lastCheck': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'lastCheck': DateTime.now().toIso8601String(),
      };
    }
  }
}

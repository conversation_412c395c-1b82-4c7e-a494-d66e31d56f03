import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/developer_mode.dart';
import '../../utils/app_colors.dart';
import 'movie_management_page.dart';
import 'user_management_page.dart';
import 'ticket_management_page.dart';
import 'theater_management_page.dart';
import 'schedule_management_page.dart';
import 'paypal_test_page.dart';

class AdminHomePage extends StatelessWidget {
  const AdminHomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        title: Text('admin_panel'.tr),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('logout'.tr),
                  content: Text('confirm_logout'.tr),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text('cancel'.tr),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: Text('logout'.tr),
                    ),
                  ],
                ),
              );

              if (confirmed == true) {
                await authController.logout();
                Get.back(); 
              }
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundImage: NetworkImage(
                              authController.user?.photoUrl ??
                                  'https://ui-avatars.com/api/?name=Admin',
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Welcome, ${authController.user?.name ?? 'Admin'}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  authController.user?.email ?? '',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Welcome to the Đớp Phim Admin Dashboard. Here you can manage your app content.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              
              const Text(
                'Admin Features',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              
              GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  
                  _buildFeatureCard(
                    icon: Icons.movie,
                    title: 'Movie Management',
                    description: 'Manage movies & banners',
                    onTap: () => Get.to(() => const MovieManagementPage()),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.local_movies,
                    title: 'Theater Management',
                    description: 'Manage theaters & screens',
                    onTap: () => Get.toNamed('/admin/theaters'),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.schedule,
                    title: 'Schedule Management',
                    description: 'Manage movie schedules',
                    onTap: () => Get.toNamed('/admin/schedule'),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.confirmation_number,
                    title: 'Ticket Management',
                    description: 'Manage bookings & tickets',
                    onTap: () => Get.toNamed('/admin/tickets'),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.access_time,
                    title: 'Ticket Expiration',
                    description: 'Manage expired tickets',
                    onTap: () => Get.toNamed('/admin/ticket-expiration'),
                  ),

                  
                  Builder(
                    builder: (context) {
                      final developerMode = Get.find<DeveloperMode>();
                      if (developerMode.isDeveloperMode) {
                        return _buildFeatureCard(
                          icon: Icons.functions,
                          title: 'Test Functions',
                          description: 'Test Firebase Functions',
                          onTap: () => Get.toNamed('/admin/test-functions'),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    },
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.people,
                    title: 'User Management',
                    description: 'Manage app users',
                    onTap: () => Get.to(() => const UserManagementPage()),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.notifications,
                    title: 'Notification Management',
                    description: 'Manage notifications',
                    onTap: () => Get.toNamed('/admin/notifications'),
                  ),

                  
                  _buildFeatureCard(
                    icon: Icons.data_usage,
                    title: 'Sample Data',
                    description: 'Create sample notifications',
                    onTap: () => Get.toNamed('/admin/notification_sample_data'),
                  ),

                  
                  Builder(
                    builder: (context) {
                      final developerMode = Get.find<DeveloperMode>();
                      if (developerMode.isDeveloperMode) {
                        return _buildFeatureCard(
                          icon: Icons.payment,
                          title: 'PayPal Test',
                          description: 'Test PayPal payments',
                          onTap: () => Get.to(() => const PayPalTestPage()),
                        );
                      } else {
                        return _buildFeatureCard(
                          icon: Icons.payment,
                          title: 'Payment Settings',
                          description: 'Configure payments',
                          onTap: () {
                            Get.snackbar(
                              'Coming Soon',
                              'This feature is not yet implemented',
                              snackPosition: SnackPosition.BOTTOM,
                            );
                          },
                        );
                      }
                    },
                  ),

                  
                  Builder(
                    builder: (context) {
                      final developerMode = Get.find<DeveloperMode>();
                      if (developerMode.isDeveloperMode) {
                        return _buildFeatureCard(
                          icon: Icons.bug_report,
                          title: 'Debug Tools',
                          description: 'Developer tools',
                          onTap: () => Get.toNamed('/debug'),
                        );
                      } else {
                        return _buildFeatureCard(
                          icon: Icons.settings,
                          title: 'App Settings',
                          description: 'Configure app settings',
                          onTap: () {
                            Get.snackbar(
                              'Coming Soon',
                              'This feature is not yet implemented',
                              snackPosition: SnackPosition.BOTTOM,
                            );
                          },
                        );
                      }
                    },
                  ),
                ],
              ),

              const SizedBox(height: 24),

              
              Center(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Back to App'),
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: ClipRect(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: AppColors.primaryGradientStart,
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 9,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

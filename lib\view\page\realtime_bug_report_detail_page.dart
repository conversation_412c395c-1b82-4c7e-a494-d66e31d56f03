import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/realtime_bug_report_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/realtime_bug_report_model.dart';

class RealtimeBugReportDetailPage extends StatefulWidget {
  final String bugReportId;

  const RealtimeBugReportDetailPage({
    Key? key,
    required this.bugReportId,
  }) : super(key: key);

  @override
  State<RealtimeBugReportDetailPage> createState() =>
      _RealtimeBugReportDetailPageState();
}

class _RealtimeBugReportDetailPageState
    extends State<RealtimeBugReportDetailPage> {
  final RealtimeBugReportController _controller =
      Get.find<RealtimeBugReportController>();
  final AuthController _authController = Get.find<AuthController>();
  final TextEditingController _responseController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  String? _selectedStatus;
  int _previousMessageCount = 0;

  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.fetchBugReportDetail(widget.bugReportId);
    });
  }

  
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _responseController.dispose();
    _scrollController.dispose();
    
    _controller.stopListeningToBugReportDetail();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chi tiết báo cáo lỗi',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: Obx(() {
          if (_controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_controller.errorMessage.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _controller.errorMessage,
                    style: GoogleFonts.mulish(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      _controller.fetchBugReportDetail(widget.bugReportId);
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          final bugReport = _controller.selectedBugReport;
          if (bugReport == null) {
            return const Center(
              child: Text('Không tìm thấy báo cáo lỗi'),
            );
          }

          
          final currentMessageCount = bugReport.responses?.length ?? 0;
          if (currentMessageCount > _previousMessageCount &&
              _previousMessageCount > 0) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToBottom();
            });
          }
          _previousMessageCount = currentMessageCount;

          return _buildBugReportDetail(bugReport);
        }),
      ),
    );
  }

  Widget _buildBugReportDetail(RealtimeBugReportModel bugReport) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                bugReport.title,
                                style: GoogleFonts.mulish(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            _buildStatusChip(bugReport.status),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Báo cáo bởi: ${bugReport.reportedByName}',
                          style: GoogleFonts.mulish(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          'Ngày tạo: ${dateFormat.format(DateTime.fromMillisecondsSinceEpoch(bugReport.createdAt))}',
                          style: GoogleFonts.mulish(
                            color: Colors.grey[600],
                          ),
                        ),
                        const Divider(height: 24),
                        Text(
                          'Mô tả:',
                          style: GoogleFonts.mulish(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          bugReport.description,
                          style: GoogleFonts.mulish(),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                
                if (_authController.isAdmin ||
                    _authController.userRole == 'developer')
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Cập nhật trạng thái',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          DropdownButtonFormField<String>(
                            value: _selectedStatus,
                            decoration: InputDecoration(
                              labelText: 'Trạng thái mới',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            items: [
                              DropdownMenuItem<String>(
                                value: 'pending',
                                child: Text(
                                  'Chưa nhận',
                                  style: GoogleFonts.mulish(),
                                ),
                              ),
                              DropdownMenuItem<String>(
                                value: 'accepted',
                                child: Text(
                                  'Đã nhận',
                                  style: GoogleFonts.mulish(),
                                ),
                              ),
                              DropdownMenuItem<String>(
                                value: 'inProgress',
                                child: Text(
                                  'Đang fix',
                                  style: GoogleFonts.mulish(),
                                ),
                              ),
                              DropdownMenuItem<String>(
                                value: 'fixed',
                                child: Text(
                                  'Đã fix',
                                  style: GoogleFonts.mulish(),
                                ),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedStatus = value;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _selectedStatus == null
                                  ? null
                                  : () => _updateBugStatus(bugReport.id),
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                backgroundColor: Colors.blue,
                              ),
                              child: Text(
                                'Cập nhật trạng thái',
                                style: GoogleFonts.mulish(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () => _toggleUserResponse(
                                      bugReport.id,
                                      !bugReport.allowUserResponse),
                                  icon: Icon(
                                    bugReport.allowUserResponse
                                        ? Icons.block
                                        : Icons.comment,
                                  ),
                                  label: Text(
                                    bugReport.allowUserResponse
                                        ? 'Tắt phản hồi của người dùng'
                                        : 'Cho phép người dùng phản hồi',
                                    style: GoogleFonts.mulish(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    backgroundColor: bugReport.allowUserResponse
                                        ? Colors.orange
                                        : Colors.green,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 24),

                
                Text(
                  'Phản hồi (${bugReport.responses?.length ?? 0})',
                  style: GoogleFonts.mulish(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                if (bugReport.responses == null || bugReport.responses!.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: Text(
                        'Chưa có phản hồi nào',
                        style: GoogleFonts.mulish(
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: bugReport.responses!.length,
                    itemBuilder: (context, index) {
                      
                      return _buildResponseItem(bugReport.responses![index]);
                    },
                  ),
              ],
            ),
          ),
        ),

        
        if (bugReport.allowUserResponse ||
            _authController.isAdmin ||
            _authController.userRole == 'developer')
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xff283E51), Color(0xff4B79A1)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _responseController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Nhập phản hồi của bạn...',
                      hintStyle:
                          TextStyle(color: Colors.white.withOpacity(0.7)),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide:
                            BorderSide(color: Colors.white.withOpacity(0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: const BorderSide(color: Colors.white),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: 3,
                    minLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: () => _sendResponse(bugReport.id),
                    icon: const Icon(Icons.send),
                    color: Colors.white,
                    iconSize: 24,
                  ),
                ),
              ],
            ),
          ),
        
        if (!bugReport.allowUserResponse &&
            !_authController.isAdmin &&
            _authController.userRole != 'developer')
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xff283E51), Color(0xff4B79A1)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Center(
              child: Text(
                'Phản hồi đã bị tắt cho báo cáo lỗi này',
                style: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildResponseItem(RealtimeBugResponseModel response) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final isCurrentUser = response.responderId == _authController.user?.id;

    
    Color backgroundColor;
    Color textColor = Colors.white;

    if (response.responderId == 'system') {
      backgroundColor = const Color(0xff4B79A1).withOpacity(0.3);
    } else if (response.isFromDeveloper) {
      backgroundColor = const Color(0xff4B79A1).withOpacity(0.8);
    } else if (response.isFromAdmin) {
      backgroundColor = const Color(0xff283E51).withOpacity(0.8);
    } else {
      backgroundColor = isCurrentUser
          ? const Color(0xff4B79A1).withOpacity(0.6)
          : const Color(0xff283E51).withOpacity(0.4);
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      response.responderName,
                      style: GoogleFonts.mulish(
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                    if (response.isFromDeveloper)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Developer',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      )
                    else if (response.isFromAdmin)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Admin',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  response.message,
                  style: GoogleFonts.mulish(
                    color: textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  dateFormat.format(
                      DateTime.fromMillisecondsSinceEpoch(response.createdAt)),
                  style: GoogleFonts.mulish(
                    color: textColor.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
                if (response.newStatus != null) ...[
                  const SizedBox(height: 8),
                  _buildStatusChip(response.newStatus!),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String displayName;

    switch (status) {
      case 'accepted':
        color = Colors.blue;
        displayName = 'Đã nhận';
        break;
      case 'inProgress':
        color = Colors.orange;
        displayName = 'Đang fix';
        break;
      case 'fixed':
        color = Colors.green;
        displayName = 'Đã fix';
        break;
      default:
        color = Colors.grey;
        displayName = 'Chưa nhận';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        displayName,
        style: GoogleFonts.mulish(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  void _sendResponse(String bugReportId) async {
    if (_responseController.text.trim().isEmpty) {
      return;
    }

    final success = await _controller.addResponse(
      bugReportId: bugReportId,
      message: _responseController.text.trim(),
    );

    if (success) {
      _responseController.clear();
      
      _scrollToBottom();
    }
  }

  void _updateBugStatus(String bugReportId) async {
    if (_selectedStatus == null) {
      return;
    }

    final success = await _controller.updateBugStatus(
      bugReportId: bugReportId,
      newStatus: _selectedStatus!,
    );

    if (success) {
      setState(() {
        _selectedStatus = null;
      });
    }
  }

  void _toggleUserResponse(String bugReportId, bool allowUserResponse) async {
    final success = await _controller.updateAllowUserResponse(
      bugReportId: bugReportId,
      allowUserResponse: allowUserResponse,
    );

    if (success) {
      Get.snackbar(
        'Thành công',
        allowUserResponse
            ? 'Đã cho phép người dùng phản hồi'
            : 'Đã tắt phản hồi của người dùng',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: allowUserResponse ? Colors.green : Colors.orange,
        colorText: Colors.white,
      );
    }
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/ticket_model.dart';

class TestExpiredTicketCreator {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  
  static Future<String> createExpiredTestTicket({
    required String userId,
    String movieTitle = 'Test Movie',
    String theaterName = 'Test Theater',
    String screenName = 'Test Screen',
    int daysAgo = 1,
    int hoursAgo = 2,
  }) async {
    try {
      
      final now = DateTime.now();
      final expiredDateTime = now.subtract(Duration(days: daysAgo, hours: hoursAgo));
      
      final expiredDate = '${expiredDateTime.year}-${expiredDateTime.month.toString().padLeft(2, '0')}-${expiredDateTime.day.toString().padLeft(2, '0')}';
      final expiredTime = '${expiredDateTime.hour.toString().padLeft(2, '0')}:${expiredDateTime.minute.toString().padLeft(2, '0')}';

      
      final ticketData = {
        'userId': userId,
        'movieTitle': movieTitle,
        'theaterName': theaterName,
        'screenName': screenName,
        'date': expiredDate,
        'time': expiredTime,
        'status': TicketStatus.confirmed.name,
        'seats': ['A1', 'A2'], // Test seats
        'totalPrice': 200000, // 200k VND
        'bookingTime': Timestamp.fromDate(expiredDateTime.subtract(const Duration(hours: 24))),
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        
        'movieId': 'test_movie_id',
        'theaterId': 'test_theater_id',
        'screenId': 'test_screen_id',
        'scheduleId': 'test_schedule_id',
        'paymentMethod': 'test',
        'isTestTicket': true, // Mark as test ticket for easy cleanup
      };

      
      final docRef = await _firestore.collection('tickets').add(ticketData);
      
      print('Created test expired ticket: ${docRef.id}');
      print('Date: $expiredDate, Time: $expiredTime');
      print('Status: ${TicketStatus.confirmed.name}');
      
      return docRef.id;
    } catch (e) {
      print('Error creating test expired ticket: $e');
      throw Exception('Failed to create test expired ticket: $e');
    }
  }

  
  static Future<List<String>> createMultipleExpiredTestTickets({
    required String userId,
    int count = 3,
  }) async {
    final ticketIds = <String>[];
    
    for (int i = 0; i < count; i++) {
      final ticketId = await createExpiredTestTicket(
        userId: userId,
        movieTitle: 'Test Movie ${i + 1}',
        daysAgo: i + 1,
        hoursAgo: i + 2,
      );
      ticketIds.add(ticketId);
    }
    
    return ticketIds;
  }

  
  static Future<String> createSoonToExpireTestTicket({
    required String userId,
    int minutesFromNow = 2,
  }) async {
    try {
      final now = DateTime.now();
      final expireTime = now.add(Duration(minutes: minutesFromNow));
      
      final expiredDate = '${expireTime.year}-${expireTime.month.toString().padLeft(2, '0')}-${expireTime.day.toString().padLeft(2, '0')}';
      final expiredTimeString = '${expireTime.hour.toString().padLeft(2, '0')}:${expireTime.minute.toString().padLeft(2, '0')}';

      final ticketData = {
        'userId': userId,
        'movieTitle': 'Soon to Expire Test Movie',
        'theaterName': 'Test Theater',
        'screenName': 'Test Screen',
        'date': expiredDate,
        'time': expiredTimeString,
        'status': TicketStatus.confirmed.name,
        'seats': ['B1'],
        'totalPrice': 150000,
        'bookingTime': Timestamp.fromDate(now.subtract(const Duration(hours: 1))),
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'movieId': 'test_movie_id',
        'theaterId': 'test_theater_id',
        'screenId': 'test_screen_id',
        'scheduleId': 'test_schedule_id',
        'paymentMethod': 'test',
        'isTestTicket': true,
        'willExpireIn': '$minutesFromNow minutes',
      };

      final docRef = await _firestore.collection('tickets').add(ticketData);
      
      print('Created soon-to-expire test ticket: ${docRef.id}');
      print('Will expire at: $expiredDate $expiredTimeString');
      print('Current time: ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}');
      
      return docRef.id;
    } catch (e) {
      print('Error creating soon-to-expire test ticket: $e');
      throw Exception('Failed to create soon-to-expire test ticket: $e');
    }
  }

  
  static Future<void> cleanupTestTickets() async {
    try {
      final snapshot = await _firestore
          .collection('tickets')
          .where('isTestTicket', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      print('Cleaned up ${snapshot.docs.length} test tickets');
    } catch (e) {
      print('Error cleaning up test tickets: $e');
    }
  }

  
  static Future<List<Map<String, dynamic>>> getTestTickets() async {
    try {
      final snapshot = await _firestore
          .collection('tickets')
          .where('isTestTicket', isEqualTo: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting test tickets: $e');
      return [];
    }
  }
}

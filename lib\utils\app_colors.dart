import 'package:flutter/material.dart';

class AppColors {
  
  AppColors._();

  
  static const Color primaryGradientStart = Color(0xFF475569); 
  static const Color primaryGradientEnd =
      Color(0xFF64748B); 
  static const Color primaryGradientAccent = Color(0xFF10B981); 

  
  static const Color scaffoldBackground = Color(0xFF0F172A); 
  static const Color cardBackground = Color(0xFF1E293B); 
  static const Color surfaceColor = Color(0xFF334155); 

  
  static const Color primarySlate = Color(0xFF475569); 
  static const Color primaryMint = Color(0xFF10B981); 
  static const Color primaryWhite = Color(0xFFF8FAFC); 

  
  static const Color accentBlue = Color(0xFF3B82F6); 
  static const Color accentMint = Color(0xFF34D399); 
  static const Color accentWarmGray = Color(0xFF6B7280); 

  
  static const Color successGreen = Color(0xFF10B981); 
  static const Color warningOrange = Color(0xFFF59E0B); 
  static const Color errorRed = Color(0xFFEF4444); 
  static const Color infoBlue = Color(0xFF3B82F6); 

  
  static const Color textPrimary = Color(0xFFF8FAFC); 
  static const Color textSecondary = Color(0xFFCBD5E1); 
  static const Color textTertiary = Color(0xFF94A3B8); 
  static const Color textDisabled = Color(0xFF64748B); 
  static const Color textOnDark = Color(0xFFF8FAFC); 
  static const Color textOnLight = Color(0xFF1E293B); 

  
  static const Color buttonPrimary = Color(0xFF475569); 
  static const Color buttonSecondary = Color(0xFF64748B); 
  static const Color buttonDanger = Color(0xFFEF4444); 
  static const Color buttonSuccess = Color(0xFF10B981); 
  static const Color buttonWarning = Color(0xFFF59E0B); 
  static const Color buttonAccent = Color(0xFF3B82F6); 

  
  static const Color borderPrimary = Color(0xFF475569); 
  static const Color borderSecondary = Color(0xFF64748B); 
  static const Color borderAccent =
      Color(0xFF10B981); 

  
  static const Color overlayLight = Color(0x1AFFFFFF);
  static const Color overlayMedium = Color(0x33FFFFFF);
  static const Color overlayDark = Color(0x66000000);
  static const Color overlayRed = Color(0x33DC143C); 

  
  static const Color chatSystemBackground =
      Color(0xFF8B0000); 
  static const Color chatDeveloperBackground =
      Color(0xFFDC143C); 
  static const Color chatAdminBackground = Color(0xFF4CAF50); 
  static const Color chatUserBackground = Color(0xFF3C3C3C); 
  static const Color chatCurrentUserBackground =
      Color(0xFF800020); 

  
  static const Color screenStandard =
      Color(0xFFDC143C); 
  static const Color screenVip = Color(0xFFFFD700); 
  static const Color screenImax = Color(0xFF8B0000); 
  static const Color screenDolby = Color(0xFF4CAF50); 
  static const Color screenPremium = Color(0xFFFF8C00); 

  
  static const Color seatAvailable =
      Color(0x4DFFFFFF); 
  static const Color seatSelected =
      Color(0xFFDC143C); 
  static const Color seatBooked = Color(0xFF757575); 
  static const Color seatVip = Color(0xFFFFD700); 
  static const Color seatCouple = Color(0xFFFF69B4); 
  static const Color seatDisabled =
      Color(0xFF424242); 

  
  static const Color genreSelected =
      Color(0xFFFFD700); 
  static const Color genreUnselected =
      Color(0x1EDC143C); 

  
  static const Color statusPending = Color(0xFF9E9E9E); 
  static const Color statusAccepted =
      Color(0xFFDC143C); 
  static const Color statusInProgress =
      Color(0xFFFF8C00); 
  static const Color statusFixed = Color(0xFF4CAF50); 

  
  static const Color primaryBlue =
      primarySlate; 
  static const Color primaryAmber =
      primaryMint; 

  
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient primaryGradientVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  
  static const LinearGradient buttonGradient = LinearGradient(
    colors: [primaryGradientStart, primaryGradientEnd],
  );

  
  static const LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x4D8B0000), 
      Color(0x4DDC143C), 
      Color(0xB3800020), 
    ],
  );

  
  static const LinearGradient genreSelectedGradient = LinearGradient(
    colors: [Color(0xFFFFD700), Color(0xFFFFB74D)], 
  );

  static const LinearGradient genreUnselectedGradient = LinearGradient(
    colors: [Color(0x1EDC143C), Color(0x1E8B0000)], 
  );

  
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color getChatBackgroundColor({
    required String responderId,
    required bool isFromDeveloper,
    required bool isFromAdmin,
    required bool isCurrentUser,
  }) {
    if (responderId == 'system') {
      return chatSystemBackground.withOpacity(0.3);
    } else if (isFromDeveloper) {
      return chatDeveloperBackground.withOpacity(0.8);
    } else if (isFromAdmin) {
      return chatAdminBackground.withOpacity(0.8);
    } else {
      return isCurrentUser
          ? chatCurrentUserBackground.withOpacity(0.6)
          : chatUserBackground.withOpacity(0.4);
    }
  }

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return statusPending;
      case 'accepted':
        return statusAccepted;
      case 'inprogress':
      case 'in_progress':
        return statusInProgress;
      case 'fixed':
        return statusFixed;
      default:
        return statusPending;
    }
  }

  static Color getScreenTypeColor(String screenType) {
    switch (screenType.toLowerCase()) {
      case 'standard':
        return screenStandard;
      case 'vip':
        return screenVip;
      case 'imax':
        return screenImax;
      case 'dolby':
        return screenDolby;
      case 'premium':
        return screenPremium;
      default:
        return screenStandard;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/ticket_model.dart';
import '../../services/ticket_stats_service.dart';

class MovieTicketDetailPage extends StatefulWidget {
  final int movieId;
  final String movieTitle;

  const MovieTicketDetailPage({
    Key? key,
    required this.movieId,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<MovieTicketDetailPage> createState() => _MovieTicketDetailPageState();
}

class _MovieTicketDetailPageState extends State<MovieTicketDetailPage> {
  final TicketStatsService _ticketStatsService = TicketStatsService();
  final RxList<Map<String, dynamic>> _ticketsWithUserInfo =
      <Map<String, dynamic>>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final RxString _selectedStatus = 'all'.obs;
  final Rx<MovieTicketStats?> _movieStats = Rx<MovieTicketStats?>(null);

  @override
  void initState() {
    super.initState();
    _loadTickets();
  }

  Future<void> _loadTickets() async {
    try {
      _isLoading.value = true;

      
      final futures = await Future.wait([
        _ticketStatsService.getMovieTicketsWithUserInfo(widget.movieId),
        _ticketStatsService.getMovieTicketStats(widget.movieId),
      ]);

      _ticketsWithUserInfo.value = futures[0] as List<Map<String, dynamic>>;
      _movieStats.value = futures[1] as MovieTicketStats?;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách vé: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  List<Map<String, dynamic>> get _filteredTickets {
    var tickets = _ticketsWithUserInfo.toList();

    
    if (_selectedStatus.value != 'all') {
      final status = TicketStatusExtension.fromString(_selectedStatus.value);
      tickets = tickets.where((item) {
        final ticket = item['ticket'] as Ticket;
        return ticket.status == status;
      }).toList();
    }

    
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      tickets = tickets.where((item) {
        final ticket = item['ticket'] as Ticket;
        final userName = item['userName'] as String;
        final userEmail = item['userEmail'] as String;

        return ticket.bookingCode.toLowerCase().contains(query) ||
            userName.toLowerCase().contains(query) ||
            userEmail.toLowerCase().contains(query) ||
            ticket.seatsDisplay.toLowerCase().contains(query);
      }).toList();
    }

    return tickets;
  }

  Future<void> _cancelTicket(Ticket ticket) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Xác nhận hủy vé',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        content: Text(
          'Bạn có chắc chắn muốn hủy vé ${ticket.bookingCode}?\nSố tiền hoàn lại: ${ticket.finalPrice.toStringAsFixed(0)} VNĐ',
          style: GoogleFonts.mulish(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              'Không',
              style: GoogleFonts.mulish(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              'Hủy vé',
              style: GoogleFonts.mulish(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _ticketStatsService.cancelTicket(ticket.id,
            refundAmount: ticket.finalPrice);
        Get.snackbar(
          'Thành công',
          'Đã hủy vé ${ticket.bookingCode}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );
        _loadTickets(); 
      } catch (e) {
        Get.snackbar(
          'Lỗi',
          'Không thể hủy vé: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Vé của ${widget.movieTitle}',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadTickets,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            
            Obx(() {
              final stats = _movieStats.value;
              if (stats != null) {
                return Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Thống kê tổng quan',
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Số vé: số lượng giao dịch mua vé • Ghế đã bán: tổng số ghế được đặt',
                        style: GoogleFonts.mulish(
                          fontSize: 10,
                          color: Colors.white.withOpacity(0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatItem(
                              'Tổng ghế',
                              '${stats.totalAvailableTickets}',
                              Colors.blue[300]!,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'Ghế đã bán',
                              '${stats.soldTickets}',
                              Colors.green[300]!,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'Số vé',
                              '${stats.totalTickets}',
                              Colors.orange[300]!,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'Doanh thu',
                              '${(stats.totalRevenue / 1000).toStringAsFixed(0)}K',
                              Colors.amber[300]!,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  
                  TextField(
                    onChanged: (value) => _searchQuery.value = value,
                    autofocus: false, 
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText:
                          'Tìm kiếm theo mã vé, tên người mua, email, ghế...',
                      hintStyle:
                          TextStyle(color: Colors.white.withOpacity(0.7)),
                      prefixIcon: const Icon(Icons.search, color: Colors.white),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  
                  Row(
                    children: [
                      Text(
                        'Trạng thái: ',
                        style: GoogleFonts.mulish(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Expanded(
                        child: Obx(() => DropdownButton<String>(
                              value: _selectedStatus.value,
                              dropdownColor: const Color(0xFF1A1A2E),
                              style: GoogleFonts.mulish(color: Colors.white),
                              underline: Container(),
                              items: const [
                                DropdownMenuItem(
                                    value: 'all', child: Text('Tất cả')),
                                DropdownMenuItem(
                                    value: 'confirmed',
                                    child: Text('Đã xác nhận')),
                                DropdownMenuItem(
                                    value: 'used', child: Text('Đã sử dụng')),
                                DropdownMenuItem(
                                    value: 'cancelled', child: Text('Đã hủy')),
                                DropdownMenuItem(
                                    value: 'expired', child: Text('Hết hạn')),
                              ],
                              onChanged: (value) =>
                                  _selectedStatus.value = value ?? 'all',
                            )),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            
            Expanded(
              child: Obx(() {
                if (_isLoading.value) {
                  return const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  );
                }

                final filteredTickets = _filteredTickets;

                if (filteredTickets.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.confirmation_number_outlined,
                          size: 64,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Không có vé nào',
                          style: GoogleFonts.mulish(
                            fontSize: 18,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredTickets.length,
                  itemBuilder: (context, index) {
                    final item = filteredTickets[index];
                    final ticket = item['ticket'] as Ticket;
                    final userName = item['userName'] as String;
                    final userEmail = item['userEmail'] as String;

                    return _buildTicketCard(ticket, userName, userEmail);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTicketCard(Ticket ticket, String userName, String userEmail) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          
          Row(
            children: [
              Expanded(
                child: Text(
                  'Mã vé: ${ticket.bookingCode}',
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[200],
                  ),
                ),
              ),
              _buildStatusBadge(ticket.status),
            ],
          ),
          const SizedBox(height: 12),

          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thông tin người mua:',
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  userName,
                  style: GoogleFonts.mulish(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (userEmail.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    userEmail,
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 12),

          
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${ticket.theaterName} • ${ticket.screenName}',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${ticket.date} • ${ticket.time}',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Ghế: ${ticket.seatsDisplay}',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: Colors.amber,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${ticket.finalPrice.toStringAsFixed(0)} VNĐ',
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[300],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Mua: ${ticket.purchaseDate.day}/${ticket.purchaseDate.month}/${ticket.purchaseDate.year}',
                    style: GoogleFonts.mulish(
                      fontSize: 10,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                ],
              ),
            ],
          ),

          
          if (ticket.status == TicketStatus.confirmed) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _cancelTicket(ticket),
                  icon: const Icon(Icons.cancel, size: 16, color: Colors.red),
                  label: Text(
                    'Hủy vé',
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 10,
            color: Colors.white.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStatusBadge(TicketStatus status) {
    Color color;
    switch (status) {
      case TicketStatus.confirmed:
        color = Colors.green;
        break;
      case TicketStatus.used:
        color = Colors.blue;
        break;
      case TicketStatus.cancelled:
        color = Colors.red;
        break;
      case TicketStatus.expired:
        color = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        status.displayName,
        style: GoogleFonts.mulish(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

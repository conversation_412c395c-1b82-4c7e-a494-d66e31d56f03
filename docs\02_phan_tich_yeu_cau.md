

D<PERSON> án "<PERSON>ớ<PERSON> Phim" đ<PERSON><PERSON><PERSON> phát triển dựa trên việc phân tích chi tiết các yêu cầu từ người dùng cuối, rạp chiếu phim và các bên liên quan khác. <PERSON>ệ thống được thiết kế để đáp ứng nhu cầu đặt vé xem phim trực tuyến với trải nghiệm người dùng tối ưu và quản lý hiệu quả cho các rạp chiếu.

- **Khảo sát người dùng:** Phỏng vấn 50+ người dùng về thói quen xem phim
- **<PERSON>ân tích đối thủ:** Nghiên cứu các ứng dụng đặt vé hiện có (CGV, Galaxy, Lotte)
- **Tham khảo chuyên gia:** Trao đổi với quản lý rạp chiếu về quy trình vận hành
- **<PERSON><PERSON><PERSON><PERSON> cứu thị trường:** <PERSON>ân tích xu hướng công nghệ và hành vi người tiêu dùng

- **Yêu cầu chức năng (Functional Requirements):** Các tính năng cụ thể mà hệ thống phải có
- **Yêu cầu phi chức năng (Non-functional Requirements):** Hiệu suất, bảo mật, khả năng mở rộng
- **Yêu cầu ràng buộc (Constraints):** Giới hạn về công nghệ, thời gian, ngân sách
- **Yêu cầu giao diện (Interface Requirements):** Tương tác với hệ thống bên ngoài

**Mô tả:** Người dùng có thể tạo tài khoản mới để sử dụng ứng dụng

**Đầu vào:**
- Email (bắt buộc): Địa chỉ email hợp lệ
- Mật khẩu (bắt buộc): Tối thiểu 6 ký tự, có ít nhất 1 chữ cái và 1 số
- Tên hiển thị (bắt buộc): 2-50 ký tự, không chứa ký tự đặc biệt
- Số điện thoại (tùy chọn): Định dạng +84xxxxxxxxx

**Đầu ra:**
- Tài khoản được tạo thành công trong Firebase Auth
- User document được tạo trong Firestore collection 'users'
- Email xác thực được gửi tự động
- Avatar mặc định được tạo từ UI Avatars API
- Chuyển hướng đến trang xác thực email

**Quy tắc nghiệp vụ:**
- Email phải unique trong hệ thống
- Mật khẩu được hash bằng Firebase Auth
- Tên hiển thị không được chứa từ ngữ không phù hợp (blacklist)
- Tự động gán role 'user' cho tài khoản mới
- Tạo user preferences mặc định (ngôn ngữ, thông báo)
- Log hoạt động đăng ký vào Analytics

**Validation Rules:**
- Email: Regex pattern RFC 5322 compliant
- Password: Minimum 6 chars, max 128 chars
- Display name: Trim whitespace, no HTML tags
- Phone: Optional but must be valid international format

**Error Handling:**
- Email đã tồn tại → "Email này đã được sử dụng"
- Mật khẩu yếu → "Mật khẩu phải có ít nhất 6 ký tự"
- Network error → "Không thể kết nối, vui lòng thử lại"
- Firebase error → Log chi tiết, hiển thị thông báo generic

**Mô tả:** Người dùng đăng nhập vào hệ thống với nhiều phương thức

**Đầu vào:**
- **Phương thức 1 - Email/Password:**
  - Email: Địa chỉ email đã đăng ký
  - Password: Mật khẩu tương ứng
- **Phương thức 2 - Google Sign-In:**
  - Google account credentials
  - Permissions: email, profile

**Đầu ra:**
- Firebase Auth token được tạo
- User session được khởi tạo
- User data được load từ Firestore
- Controllers được initialize
- Chuyển hướng đến RootPage(index: 0)

**Quy tắc nghiệp vụ:**
- Tối đa 5 lần thử đăng nhập sai trong 15 phút
- Sau 5 lần sai → khóa tạm thời 30 phút
- Remember me: Lưu credentials an toàn với SharedPreferences
- Auto-login: Kiểm tra Firebase Auth state khi mở app
- Google Sign-In: Tự động tạo account nếu chưa tồn tại

**Session Management:**
- Token expiry: 1 giờ (có thể refresh)
- Auto refresh token trước khi hết hạn 5 phút
- Logout tự động khi token invalid
- Multi-device support với Firebase Auth

**Security Features:**
- Rate limiting cho login attempts
- Device fingerprinting (optional)
- Suspicious activity detection
- Login notification email

**Mô tả:** Người dùng có thể xem và chỉnh sửa thông tin cá nhân một cách toàn diện

**Chức năng chi tiết:**

**3.1 Thông tin cơ bản:**
- Xem/sửa tên hiển thị (2-50 ký tự)
- Xem email (không thể sửa, chỉ có thể thay đổi qua Firebase)
- Thêm/sửa số điện thoại với OTP verification
- Chọn ngày sinh (optional, để tính tuổi cho phim phù hợp)
- Chọn giới tính (optional, cho recommendations)

**3.2 Ảnh đại diện:**
- Upload từ gallery hoặc camera
- Crop và resize tự động (300x300px)
- Lưu trữ trên Firebase Storage
- Tự động tạo thumbnail
- Xóa ảnh cũ khi upload ảnh mới
- Fallback về UI Avatars nếu không có ảnh

**3.3 Bảo mật:**
- Thay đổi mật khẩu (yêu cầu mật khẩu cũ)
- Xem danh sách thiết bị đã đăng nhập
- Đăng xuất khỏi tất cả thiết bị
- Bật/tắt two-factor authentication

**3.4 Preferences:**
- Chọn ngôn ngữ (Tiếng Việt/English)
- Cài đặt thông báo (push, email, SMS)
- Chọn rạp yêu thích (để hiển thị ưu tiên)
- Thể loại phim yêu thích
- Múi giờ và định dạng ngày

**3.5 Lịch sử hoạt động:**
- Lịch sử đặt vé (có thể filter theo trạng thái, ngày)
- Lịch sử thanh toán với chi tiết giao dịch
- Phim đã xem và đánh giá
- Hoạt động đăng nhập gần đây

**Validation & Business Rules:**
- Tên hiển thị: Không trùng với users khác
- Số điện thoại: Verify bằng OTP trước khi lưu
- Ảnh đại diện: Max 5MB, format JPG/PNG
- Password change: Require current password
- Data export: GDPR compliance

**Mô tả:** Hệ thống hỗ trợ 3 cấp độ người dùng với permissions chi tiết

**Các vai trò và quyền hạn:**

**4.1 User (Người dùng thường):**
- **Quyền đọc:**
  - Xem danh sách phim, rạp, lịch chiếu
  - Xem chi tiết phim, trailer
  - Xem thông tin rạp chiếu
  - Đọc thông báo cá nhân
- **Quyền ghi:**
  - Đặt vé cho bản thân
  - Hủy vé (trong thời hạn cho phép)
  - Cập nhật thông tin cá nhân
  - Đánh giá phim đã xem
  - Thêm phim vào danh sách yêu thích
- **Giới hạn:**
  - Không thể xem thông tin người dùng khác
  - Không thể truy cập admin functions
  - Tối đa 10 vé/giao dịch

**4.2 Admin (Quản trị viên):**
- **Tất cả quyền của User +**
- **Quyền quản lý nội dung:**
  - CRUD operations cho movies, theaters, screens
  - Quản lý lịch chiếu (tạo, sửa, xóa)
  - Import/export dữ liệu hàng loạt
  - Quản lý banner và promotions
- **Quyền quản lý người dùng:**
  - Xem danh sách tất cả users
  - Khóa/mở khóa tài khoản
  - Reset password cho users
  - Xem lịch sử hoạt động users
- **Quyền báo cáo:**
  - Truy cập dashboard analytics
  - Export báo cáo doanh thu
  - Xem thống kê hệ thống
- **Quyền hệ thống:**
  - Gửi thông báo mass
  - Quản lý cài đặt hệ thống
  - Backup/restore dữ liệu

**4.3 Developer (Nhà phát triển):**
- **Tất cả quyền của Admin +**
- **Debug tools:**
  - Truy cập debug console
  - Xem system logs real-time
  - Test Firebase Functions
  - Database query tools
- **System administration:**
  - Quản lý Firebase rules
  - Deploy code updates
  - Monitor system performance
  - Access error tracking

**Role Assignment & Management:**
- Default role: 'user' cho tài khoản mới
- Admin access: Tap username 7 lần liên tiếp để hiện admin login
- Role changes: Chỉ developer có thể thay đổi roles
- Permission inheritance: Developer > Admin > User
- Role verification: Kiểm tra quyền ở cả client và server side

**Security Implementation:**
- Firestore Security Rules enforce permissions
- Client-side role checking cho UI
- Server-side validation trong Cloud Functions
- Audit log cho tất cả admin actions

**Mô tả:** Hiển thị danh sách phim theo các danh mục với tính năng lọc và sắp xếp nâng cao

**Chức năng chi tiết:**

**5.1 Danh mục phim:**
- **Phim đang chiếu (Now Playing):**
  - Hiển thị phim có lịch chiếu trong 7 ngày tới
  - Sắp xếp theo độ phổ biến hoặc ngày ra mắt
  - Badge "HOT" cho phim có nhiều lượt đặt vé
- **Phim sắp chiếu (Coming Soon):**
  - Phim sẽ ra mắt trong 30 ngày tới
  - Cho phép đặt trước vé (pre-booking)
  - Thông báo khi có lịch chiếu
- **Phim theo thể loại:**
  - 15+ thể loại: Action, Comedy, Drama, Horror, Romance, Sci-Fi, etc.
  - Hiển thị số lượng phim trong mỗi thể loại
  - Có thể chọn nhiều thể loại cùng lúc
- **Phim yêu thích cá nhân:**
  - Danh sách phim user đã thêm vào favorites
  - Sync across devices
  - Thông báo khi có lịch chiếu mới

**5.2 Tính năng lọc và sắp xếp:**
- **Lọc theo:**
  - Thể loại (multi-select)
  - Độ tuổi (G, PG, PG-13, R)
  - Thời lượng (< 90 phút, 90-120 phút, > 120 phút)
  - Ngôn ngữ (Tiếng Việt, English, có phụ đề)
  - Rạp chiếu (chỉ hiển thị phim có ở rạp đã chọn)
- **Sắp xếp theo:**
  - Độ phổ biến (số lượt đặt vé)
  - Đánh giá (rating từ cao đến thấp)
  - Ngày ra mắt (mới nhất trước)
  - Tên phim (A-Z)
  - Giá vé (thấp đến cao)

**5.3 Hiển thị thông tin:**
- **Card layout với:**
  - Poster chất lượng cao (300x450px)
  - Tên phim (tiếng Việt và English)
  - Thể loại và thời lượng
  - Rating stars (từ user reviews)
  - Giá vé từ (lowest price)
  - Badge trạng thái (Now Playing, Coming Soon, Hot)
- **List layout (compact):**
  - Poster nhỏ (100x150px)
  - Thông tin cơ bản
  - Quick action buttons

**5.4 Performance & UX:**
- Lazy loading với pagination (20 phim/page)
- Image caching và progressive loading
- Pull-to-refresh để cập nhật
- Skeleton loading khi fetch data
- Search suggestion khi typing

**Mô tả:** Hệ thống tìm kiếm thông minh với nhiều tiêu chí và gợi ý tự động

**Tính năng tìm kiếm:**

**6.1 Tìm kiếm cơ bản:**
- **Text search:**
  - Tên phim (tiếng Việt, English, tên gốc)
  - Fuzzy search (chấp nhận lỗi chính tả)
  - Auto-complete với suggestions
  - Search history (10 từ khóa gần nhất)
- **Voice search:**
  - Speech-to-text integration
  - Hỗ trợ tiếng Việt và English
  - Xử lý accent và dialect

**6.2 Tìm kiếm nâng cao:**
- **Theo cast & crew:**
  - Diễn viên chính và phụ
  - Đạo diễn, nhà sản xuất
  - Auto-suggest từ database
- **Theo metadata:**
  - Năm sản xuất (range picker)
  - Quốc gia sản xuất
  - Studio/hãng phim
  - Awards và nominations

**6.3 Filter kết hợp:**
- **Multi-criteria search:**
  - Combine text + filters
  - Save search preferences
  - Quick filter buttons
- **Location-based:**
  - Phim đang chiếu gần user
  - Theo rạp yêu thích
  - Trong bán kính X km

**6.4 Search results:**
- **Hiển thị kết quả:**
  - Relevance scoring
  - Highlight matched terms
  - "Did you mean?" suggestions
  - No results → suggest similar movies
- **Search analytics:**
  - Track popular searches
  - Improve search algorithm
  - Personalized results

**Mô tả:** Trang chi tiết phim toàn diện với đầy đủ thông tin và tương tác

**Thông tin chi tiết:**

**7.1 Visual elements:**
- **Hero section:**
  - Backdrop image full-width
  - Poster overlay (300x450px)
  - Play trailer button prominent
  - Gradient overlay cho readability
- **Image gallery:**
  - Multiple posters và stills
  - Swipeable gallery
  - Zoom functionality
  - High-resolution images

**7.2 Movie metadata:**
- **Thông tin cơ bản:**
  - Tên phim (multiple languages)
  - Tagline và synopsis
  - Thể loại (clickable tags)
  - Thời lượng runtime
  - Ngày ra mắt (release date)
  - Độ tuổi rating (G, PG, PG-13, R)
  - Ngôn ngữ và phụ đề
- **Production info:**
  - Đạo diễn và screenwriter
  - Nhà sản xuất và studio
  - Ngân sách và box office
  - Quốc gia sản xuất

**7.3 Cast & Crew:**
- **Diễn viên:**
  - Photo, tên và vai diễn
  - Clickable để xem filmography
  - Horizontal scrollable list
- **Crew chính:**
  - Director, Producer, Writer
  - Composer, Cinematographer
  - Link to their other works

**7.4 User engagement:**
- **Rating & Reviews:**
  - Average rating (5-star system)
  - User review count
  - Recent reviews preview
  - "Write a review" button
- **Social features:**
  - Add to favorites (heart icon)
  - Share movie (social media)
  - "Want to watch" list
  - Friend recommendations

**7.5 Showtimes integration:**
- **Lịch chiếu:**
  - Today's showtimes preview
  - "Book tickets" CTA button
  - Nearby theaters list
  - Quick date selector

**Mô tả:** Trình phát video tích hợp với trải nghiệm xem trailer tối ưu

**Tính năng phát video:**

**8.1 Video player:**
- **Media Kit integration:**
  - Hardware acceleration
  - Multiple format support (MP4, HLS)
  - Adaptive bitrate streaming
  - Offline caching capability
- **Player controls:**
  - Play/pause với space bar
  - Seek bar với preview thumbnails
  - Volume control
  - Playback speed (0.5x, 1x, 1.25x, 1.5x, 2x)
  - Picture-in-picture mode

**8.2 Viewing experience:**
- **Fullscreen mode:**
  - Landscape orientation lock
  - Hide system UI
  - Gesture controls (tap to show/hide controls)
  - Double-tap to seek ±10 seconds
- **Quality settings:**
  - Auto quality based on connection
  - Manual quality selection (480p, 720p, 1080p)
  - Data saver mode
  - WiFi-only streaming option

**8.3 Multiple trailers:**
- **Trailer types:**
  - Official trailer
  - Teaser trailer
  - Behind-the-scenes
  - Cast interviews
- **Playlist functionality:**
  - Auto-play next trailer
  - Shuffle mode
  - Continue watching from last position

**8.4 Performance & Analytics:**
- **Optimization:**
  - Preload next video
  - Background downloading
  - Memory management
  - Battery optimization
- **Analytics tracking:**
  - View duration
  - Completion rate
  - Quality preferences
  - Popular trailers

**Mô tả:** Quy trình đặt vé được chia thành 5 bước rõ ràng

**Bước 1: Chọn phim**
- Hiển thị danh sách phim đang chiếu
- Xem chi tiết phim và trailer
- Chọn phim muốn xem

**Bước 2: Chọn rạp chiếu**
- Hiển thị danh sách rạp có chiếu phim
- Thông tin địa chỉ, khoảng cách
- Lọc theo khu vực

**Bước 3: Chọn suất chiếu**
- Hiển thị lịch chiếu theo ngày
- Thông tin giờ chiếu, phòng chiếu
- Giá vé cho từng suất

**Bước 4: Chọn ghế ngồi**
- Sơ đồ ghế thời gian thực
- Phân biệt ghế trống/đã đặt/đang chọn
- Tính năng chọn ghế liền kề tự động

**Bước 5: Thanh toán**
- Xác nhận thông tin đặt vé
- Chọn phương thức thanh toán
- Xử lý thanh toán an toàn

**Mô tả:** Quản lý trạng thái ghế ngồi theo thời gian thực
**Trạng thái ghế:**
- **Trống:** Có thể chọn
- **Đang chọn:** Người khác đang chọn (màu vàng)
- **Đã đặt:** Không thể chọn (màu đỏ)
- **Đang giữ:** Người dùng hiện tại đang chọn (màu xanh)

**Quy tắc nghiệp vụ:**
- Giữ ghế tối đa 10 phút
- Tự động hủy nếu không thanh toán
- Cập nhật trạng thái real-time cho tất cả users
- Ngăn chặn đặt trùng ghế

**Mô tả:** Tự động tính toán tổng tiền dựa trên ghế đã chọn
**Yếu tố ảnh hưởng giá:**
- Loại ghế (thường, VIP, couple)
- Suất chiếu (sáng, chiều, tối, đêm)
- Ngày trong tuần (thường, cuối tuần)
- Khuyến mãi đặc biệt

**Mô tả:** Xử lý thanh toán qua PayPal
**Tính năng:**
- Chuyển đổi VND sang USD tự động
- Sandbox mode cho testing
- Production mode cho thực tế
- Xử lý callback thành công/thất bại

**Mô tả:** Theo dõi và quản lý các giao dịch thanh toán
**Trạng thái giao dịch:**
- Pending: Đang xử lý
- Completed: Thành công
- Failed: Thất bại
- Cancelled: Đã hủy
- Refunded: Đã hoàn tiền

**Mô tả:** Xử lý các trường hợp thanh toán không thành công
**Quy trình:**
- Giữ ghế trong 15 phút sau lỗi
- Cho phép thử lại thanh toán
- Tự động hủy vé nếu không thành công
- Thông báo lỗi chi tiết cho người dùng

**Mô tả:** Tạo và quản lý vé điện tử
**Thông tin vé:**
- Mã đặt vé duy nhất
- QR Code để quét tại rạp
- Thông tin phim, rạp, suất chiếu
- Ghế ngồi đã đặt
- Thời gian đặt vé

**Mô tả:** Người dùng có thể xem và quản lý vé đã đặt
**Phân loại:**
- Vé sắp tới (upcoming)
- Vé đã qua (past)
- Vé đã hủy (cancelled)

**Mô tả:** Cho phép hủy vé trong điều kiện nhất định
**Quy tắc:**
- Chỉ hủy được trước 2 giờ chiếu
- Hoàn tiền 80% giá vé
- Tự động cập nhật trạng thái ghế
- Gửi thông báo xác nhận

**Mô tả:** Hệ thống thông báo sử dụng Firebase Realtime Database
**Loại thông báo:**
- Xác nhận đặt vé thành công
- Nhắc nhở trước giờ chiếu
- Khuyến mãi và ưu đãi đặc biệt
- Phim mới ra mắt
- Thay đổi lịch chiếu

**Mô tả:** Người dùng có thể quản lý thông báo
**Tính năng:**
- Đánh dấu đã đọc/chưa đọc
- Xóa thông báo
- Đánh dấu tất cả đã đọc
- Cài đặt loại thông báo muốn nhận

**Mô tả:** Admin có thể quản lý thông tin rạp chiếu
**Chức năng:**
- Thêm/sửa/xóa rạp chiếu
- Quản lý thông tin địa chỉ, liên hệ
- Cập nhật trạng thái hoạt động
- Import hàng loạt từ Excel/CSV

**Mô tả:** Quản lý các phòng chiếu trong rạp
**Chức năng:**
- Tạo sơ đồ ghế ngồi
- Cấu hình loại ghế (thường, VIP, couple)
- Thiết lập sức chứa phòng
- Khóa/mở phòng chiếu

**Mô tả:** Lập và quản lý lịch chiếu phim
**Chức năng:**
- Tạo suất chiếu mới
- Gán phim vào phòng chiếu
- Thiết lập giá vé theo suất
- Xử lý xung đột lịch chiếu

**Mô tả:** Cung cấp báo cáo chi tiết cho admin
**Loại báo cáo:**
- Doanh thu theo ngày/tháng/năm
- Số lượng vé bán theo phim
- Tỷ lệ lấp đầy phòng chiếu
- Thống kê người dùng hoạt động

- Tải trang chủ: < 2 giây
- Tìm kiếm phim: < 1 giây
- Cập nhật trạng thái ghế: < 500ms
- Xử lý thanh toán: < 10 giây

- Hỗ trợ 1000 người dùng đồng thời
- Xử lý 100 giao dịch/phút
- Uptime 99.5%
- Tự động scale khi cần thiết

- Kích thước ứng dụng < 50MB
- Sử dụng RAM < 200MB
- Tối ưu hình ảnh và video
- Lazy loading cho danh sách dài

- Firebase Authentication với Google Sign-In
- JWT token với thời gian hết hạn
- Role-based access control
- Two-factor authentication (tùy chọn)

- Mã hóa dữ liệu nhạy cảm
- HTTPS cho tất cả API calls
- Firestore Security Rules
- Input validation và sanitization

- PCI DSS compliance qua PayPal
- Không lưu trữ thông tin thẻ
- Mã hóa thông tin giao dịch
- Audit trail cho tất cả giao dịch

- Material Design guidelines
- Responsive design cho mọi kích thước màn hình
- Dark/Light theme
- Accessibility support (WCAG 2.1)

- Hỗ trợ tiếng Việt và tiếng Anh
- Chuyển đổi ngôn ngữ real-time
- Localization cho định dạng ngày, tiền tệ
- RTL support (chuẩn bị cho tương lai)

- Onboarding flow cho người dùng mới
- Offline mode cho một số tính năng
- Push notifications
- Deep linking support

- Clean Architecture với MVVM pattern
- Dependency injection với GetX
- Unit test coverage > 80%
- Code documentation đầy đủ

- Firebase Analytics integration
- Crashlytics cho error tracking
- Performance monitoring
- Custom events tracking

- Automated testing pipeline
- Automated deployment
- Code quality checks
- Security vulnerability scanning

- Microservices với Firebase Functions
- Horizontal scaling capability
- Load balancing
- Database sharding (khi cần)

- RESTful API design
- GraphQL support (tương lai)
- Webhook support
- Third-party integrations

- **Frontend:** Flutter 3.x
- **Backend:** Firebase (Firestore, Functions, Auth)
- **Payment:** PayPal SDK
- **Video:** Media Kit
- **State Management:** GetX

- **Mobile:** Android 7.0+, iOS 11.0+
- **Web:** Chrome, Firefox, Safari, Edge
- **Responsive:** Tablet và desktop support

- GDPR compliance cho dữ liệu người dùng
- Luật bảo vệ người tiêu dùng Việt Nam
- App Store và Google Play policies
- Accessibility standards

Hệ thống "Đớp Phim" được thiết kế để phục vụ 3 nhóm người dùng chính với các quyền hạn khác nhau. Sơ đồ Use Case dưới đây mô tả toàn bộ các chức năng của hệ thống và mối quan hệ giữa các actors.

**Hình 2.1: Use Case Diagram - Hệ thống Đặt Vé Xem Phim "Đớp Phim"**

*[Use Case Diagram đã được render ở trên]*

**Actors (Tác nhân):**
- **👤 Người dùng (User):** Khách hàng cuối sử dụng ứng dụng để đặt vé
- **👨‍💼 Quản trị viên (Admin):** Nhân viên quản lý rạp chiếu, có tất cả quyền của User
- **👨‍💻 Nhà phát triển (Developer):** Có tất cả quyền của Admin + quyền debug/monitor
- **💳 PayPal:** Hệ thống thanh toán bên ngoài
- **☁️ Firebase:** Dịch vụ backend cung cấp database, auth, storage

**Phân loại Use Cases:**
- **🔵 Chức năng Người dùng (15 use cases):** Các tính năng cơ bản cho việc đặt vé
- **🟣 Chức năng Quản trị (9 use cases):** Quản lý hệ thống và dữ liệu
- **🟢 Chức năng Phát triển (5 use cases):** Debug và monitoring
- **🟠 Hệ thống bên ngoài (4 use cases):** Tích hợp với external services

**Mối quan hệ:**
- **Include (bao gồm):** Use case A luôn cần use case B để hoàn thành
- **Extend (mở rộng):** Use case B có thể được thực hiện như phần mở rộng của use case A

**Actor:** Người dùng đã đăng nhập
**Mục tiêu:** Đặt vé xem phim thành công
**Điều kiện tiên quyết:** Người dùng đã đăng nhập, có phim đang chiếu

**Luồng chính:**
1. Người dùng chọn phim từ danh sách
2. Hệ thống hiển thị chi tiết phim và danh sách rạp
3. Người dùng chọn rạp chiếu
4. Hệ thống hiển thị lịch chiếu
5. Người dùng chọn suất chiếu
6. Hệ thống hiển thị sơ đồ ghế
7. Người dùng chọn ghế ngồi
8. Hệ thống tính toán tổng tiền
9. Người dùng xác nhận và chọn phương thức thanh toán
10. Hệ thống xử lý thanh toán
11. Hệ thống tạo vé điện tử và gửi thông báo

**Luồng thay thế:**
- 2a. Không có rạp nào chiếu phim → Thông báo lỗi
- 5a. Không có suất chiếu → Gợi ý ngày khác
- 7a. Ghế đã được đặt → Cập nhật sơ đồ, yêu cầu chọn lại
- 10a. Thanh toán thất bại → Giữ ghế 15 phút, cho phép thử lại

**Actor:** Admin
**Mục tiêu:** Tạo lịch chiếu mới cho phim
**Điều kiện tiên quyết:** Admin đã đăng nhập, có phim và rạp trong hệ thống

**Luồng chính:**
1. Admin truy cập trang quản lý lịch chiếu
2. Admin chọn "Tạo lịch chiếu mới"
3. Hệ thống hiển thị form tạo lịch chiếu
4. Admin chọn phim
5. Admin chọn rạp và phòng chiếu
6. Admin thiết lập thời gian chiếu
7. Admin thiết lập giá vé
8. Hệ thống kiểm tra xung đột lịch chiếu
9. Admin xác nhận tạo lịch chiếu
10. Hệ thống lưu lịch chiếu và thông báo thành công

**Luồng thay thế:**
- 8a. Có xung đột lịch chiếu → Thông báo lỗi, yêu cầu chọn thời gian khác
- 8b. Phòng chiếu không hoạt động → Thông báo lỗi

**US-001:** Là một người dùng, tôi muốn duyệt danh sách phim đang chiếu để chọn phim muốn xem
- **Acceptance Criteria:**
  - Hiển thị poster, tên phim, thể loại, thời lượng
  - Có thể lọc theo thể loại
  - Có thể sắp xếp theo tên, ngày ra mắt, đánh giá
  - Load nhanh với lazy loading

**US-002:** Là một người dùng, tôi muốn xem chi tiết phim để quyết định có xem hay không
- **Acceptance Criteria:**
  - Hiển thị đầy đủ thông tin phim
  - Có thể xem trailer
  - Xem đánh giá từ người dùng khác
  - Nút "Đặt vé" rõ ràng

**US-003:** Là một người dùng, tôi muốn chọn ghế ngồi trực quan để có vị trí tốt nhất
- **Acceptance Criteria:**
  - Sơ đồ ghế rõ ràng, dễ hiểu
  - Phân biệt được ghế trống/đã đặt/đang chọn
  - Cập nhật real-time khi có người khác đặt
  - Tự động tính tiền khi chọn ghế

**US-004:** Là một người dùng mới, tôi muốn đăng ký tài khoản dễ dàng để sử dụng ứng dụng
- **Acceptance Criteria:**
  - Form đăng ký đơn giản, ít trường bắt buộc
  - Có thể đăng ký bằng Google
  - Xác thực email tự động
  - Tạo avatar mặc định

**US-005:** Là một người dùng, tôi muốn quản lý thông tin cá nhân để cập nhật khi cần
- **Acceptance Criteria:**
  - Có thể thay đổi tên, ảnh đại diện
  - Thay đổi mật khẩu an toàn
  - Xem lịch sử giao dịch
  - Cài đặt thông báo

**US-006:** Là một người dùng, tôi muốn thanh toán an toàn và nhanh chóng
- **Acceptance Criteria:**
  - Hỗ trợ PayPal
  - Quy trình thanh toán ít bước
  - Thông báo rõ ràng về trạng thái thanh toán
  - Có thể thử lại khi thất bại

**US-007:** Là một admin, tôi muốn quản lý rạp chiếu hiệu quả
- **Acceptance Criteria:**
  - Thêm/sửa/xóa rạp dễ dàng
  - Import hàng loạt từ Excel
  - Quản lý trạng thái hoạt động
  - Báo cáo chi tiết

**US-008:** Là một admin, tôi muốn theo dõi doanh thu và hiệu suất
- **Acceptance Criteria:**
  - Dashboard tổng quan
  - Báo cáo theo thời gian
  - Thống kê phim bán chạy
  - Export dữ liệu

- **Hiệu suất:** Tất cả trang phải load trong vòng 3 giây
- **Tương thích:** Hoạt động trên Android 7.0+ và iOS 11.0+
- **Bảo mật:** Tất cả dữ liệu nhạy cảm phải được mã hóa
- **Khả năng sử dụng:** Người dùng mới có thể đặt vé trong vòng 5 phút

- **Chuyển đổi:** Tỷ lệ chuyển đổi từ xem phim đến đặt vé > 15%
- **Retention:** Người dùng quay lại sau 30 ngày > 40%
- **Satisfaction:** Điểm đánh giá trung bình > 4.0/5.0
- **Support:** Thời gian phản hồi hỗ trợ < 24 giờ

---

*Phần tiếp theo: 3. Nghiên cứu thị trường và đối thủ cạnh tranh*

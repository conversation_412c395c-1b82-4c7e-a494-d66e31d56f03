import 'package:cloud_firestore/cloud_firestore.dart';

enum ShowtimeStatus { upcoming, ongoing, ended, cancelled, full }

extension ShowtimeStatusExtension on ShowtimeStatus {
  String get name {
    switch (this) {
      case ShowtimeStatus.upcoming:
        return 'upcoming';
      case ShowtimeStatus.ongoing:
        return 'ongoing';
      case ShowtimeStatus.ended:
        return 'ended';
      case ShowtimeStatus.cancelled:
        return 'cancelled';
      case ShowtimeStatus.full:
        return 'full';
    }
  }

  static ShowtimeStatus fromString(String? value) {
    switch (value) {
      case 'upcoming':
        return ShowtimeStatus.upcoming;
      case 'ongoing':
        return ShowtimeStatus.ongoing;
      case 'ended':
        return ShowtimeStatus.ended;
      case 'cancelled':
        return ShowtimeStatus.cancelled;
      case 'full':
        return ShowtimeStatus.full;
      case 'active':
        return ShowtimeStatus.upcoming;
      default:
        return ShowtimeStatus.upcoming;
    }
  }

  String get displayName {
    switch (this) {
      case ShowtimeStatus.upcoming:
        return 'Sắp <PERSON>';
      case ShowtimeStatus.ongoing:
        return '<PERSON><PERSON>';
      case ShowtimeStatus.ended:
        return 'Đ<PERSON>';
      case ShowtimeStatus.cancelled:
        return 'Đ<PERSON>';
      case ShowtimeStatus.full:
        return 'Hết V<PERSON>';
    }
  }
}

class ShowtimePricing {
  final double standard;
  final double vip;
  final double couple;
  final double student;
  final double senior;

  ShowtimePricing({
    required this.standard,
    required this.vip,
    required this.couple,
    required this.student,
    required this.senior,
  });

  factory ShowtimePricing.fromJson(Map<String, dynamic> json) {
    return ShowtimePricing(
      standard: json['standard']?.toDouble() ?? 0.0,
      vip: json['vip']?.toDouble() ?? 0.0,
      couple: json['couple']?.toDouble() ?? 0.0,
      student: json['student']?.toDouble() ?? 0.0,
      senior: json['senior']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'standard': standard,
      'vip': vip,
      'couple': couple,
      'student': student,
      'senior': senior,
    };
  }

  ShowtimePricing copyWith({
    double? standard,
    double? vip,
    double? couple,
    double? student,
    double? senior,
  }) {
    return ShowtimePricing(
      standard: standard ?? this.standard,
      vip: vip ?? this.vip,
      couple: couple ?? this.couple,
      student: student ?? this.student,
      senior: senior ?? this.senior,
    );
  }

  double getPriceForSeatType(String seatType) {
    switch (seatType.toLowerCase()) {
      case 'vip':
        return vip;
      case 'couple':
        return couple;
      case 'student':
        return student;
      case 'senior':
        return senior;
      default:
        return standard;
    }
  }
}

class SpecialOffer {
  final String type;
  final double discount;
  final String conditions;

  SpecialOffer({
    required this.type,
    required this.discount,
    required this.conditions,
  });

  factory SpecialOffer.fromJson(Map<String, dynamic> json) {
    return SpecialOffer(
      type: json['type'] ?? '',
      discount: json['discount']?.toDouble() ?? 0.0,
      conditions: json['conditions'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'discount': discount,
      'conditions': conditions,
    };
  }

  SpecialOffer copyWith({
    String? type,
    double? discount,
    String? conditions,
  }) {
    return SpecialOffer(
      type: type ?? this.type,
      discount: discount ?? this.discount,
      conditions: conditions ?? this.conditions,
    );
  }

  String get displayName {
    switch (type) {
      case 'early_bird':
        return 'Giảm Giá Sớm';
      case 'group_discount':
        return 'Giảm Giá Nhóm';
      case 'student_discount':
        return 'Giảm Giá Sinh Viên';
      default:
        return 'Ưu Đãi Đặc Biệt';
    }
  }
}

class ShowtimeModel {
  final String id;
  final int movieId;
  final String theaterId;
  final String screenId;
  final String date;
  final String time;
  final String endTime;
  final ShowtimePricing pricing;
  final int availableSeats;
  final List<String> bookedSeats;
  final List<String> reservedSeats;
  final Map<String, dynamic> reservationData;
  final ShowtimeStatus status;
  final List<SpecialOffer> specialOffers;
  final DateTime createdAt;
  final DateTime updatedAt;

  ShowtimeModel({
    required this.id,
    required this.movieId,
    required this.theaterId,
    required this.screenId,
    required this.date,
    required this.time,
    required this.endTime,
    required this.pricing,
    required this.availableSeats,
    this.bookedSeats = const [],
    this.reservedSeats = const [],
    this.reservationData = const {},
    this.status = ShowtimeStatus.upcoming,
    this.specialOffers = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShowtimeModel.fromJson(Map<String, dynamic> json) {
    return ShowtimeModel(
      id: json['id'] ?? '',
      movieId: json['movieId'] ?? 0,
      theaterId: json['theaterId'] ?? '',
      screenId: json['screenId'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      endTime: json['endTime'] ?? '',
      pricing: ShowtimePricing.fromJson(json['pricing'] ?? {}),
      availableSeats: json['availableSeats'] ?? 0,
      bookedSeats: List<String>.from(json['bookedSeats'] ?? []),
      reservedSeats: List<String>.from(json['reservedSeats'] ?? []),
      reservationData: Map<String, dynamic>.from(json['reservationData'] ?? {}),
      status: ShowtimeStatusExtension.fromString(json['status']),
      specialOffers: (json['specialOffers'] as List<dynamic>?)
              ?.map((offer) => SpecialOffer.fromJson(offer))
              .toList() ??
          [],
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  factory ShowtimeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ShowtimeModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'movieId': movieId,
      'theaterId': theaterId,
      'screenId': screenId,
      'date': date,
      'time': time,
      'endTime': endTime,
      'pricing': pricing.toJson(),
      'availableSeats': availableSeats,
      'bookedSeats': bookedSeats,
      'reservedSeats': reservedSeats,
      'reservationData': reservationData,
      'status': status.name,
      'specialOffers': specialOffers.map((offer) => offer.toJson()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  ShowtimeModel copyWith({
    String? id,
    int? movieId,
    String? theaterId,
    String? screenId,
    String? date,
    String? time,
    String? endTime,
    ShowtimePricing? pricing,
    int? availableSeats,
    List<String>? bookedSeats,
    List<String>? reservedSeats,
    Map<String, dynamic>? reservationData,
    ShowtimeStatus? status,
    List<SpecialOffer>? specialOffers,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ShowtimeModel(
      id: id ?? this.id,
      movieId: movieId ?? this.movieId,
      theaterId: theaterId ?? this.theaterId,
      screenId: screenId ?? this.screenId,
      date: date ?? this.date,
      time: time ?? this.time,
      endTime: endTime ?? this.endTime,
      pricing: pricing ?? this.pricing,
      availableSeats: availableSeats ?? this.availableSeats,
      bookedSeats: bookedSeats ?? this.bookedSeats,
      reservedSeats: reservedSeats ?? this.reservedSeats,
      reservationData: reservationData ?? this.reservationData,
      status: status ?? this.status,
      specialOffers: specialOffers ?? this.specialOffers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  DateTime get showDateTime {
    final dateParts = date.split('-');
    final timeParts = time.split(':');
    return DateTime(
      int.parse(dateParts[0]),
      int.parse(dateParts[1]),
      int.parse(dateParts[2]),
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  DateTime get endDateTime {
    final dateParts = date.split('-');
    final timeParts = endTime.split(':');
    return DateTime(
      int.parse(dateParts[0]),
      int.parse(dateParts[1]),
      int.parse(dateParts[2]),
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  ShowtimeStatus get timeBasedStatus {
    final now = DateTime.now();
    final showTime = showDateTime;
    final endTime = endDateTime;

    if (status == ShowtimeStatus.cancelled || status == ShowtimeStatus.full) {
      return status;
    }

    if (endTime.isBefore(now)) {
      return ShowtimeStatus.ended;
    } else if (showTime.isBefore(now) && endTime.isAfter(now)) {
      return ShowtimeStatus.ongoing;
    } else {
      return ShowtimeStatus.upcoming;
    }
  }

  bool get isBookable =>
      timeBasedStatus == ShowtimeStatus.upcoming &&
      availableSeats > 0 &&
      status != ShowtimeStatus.cancelled;

  bool get isFull => availableSeats <= 0 || status == ShowtimeStatus.full;
  bool get isCancelled => status == ShowtimeStatus.cancelled;
  bool get isEnded => timeBasedStatus == ShowtimeStatus.ended;
  bool get isOngoing => timeBasedStatus == ShowtimeStatus.ongoing;
  bool get isUpcoming => timeBasedStatus == ShowtimeStatus.upcoming;

  bool get needsStatusUpdate {
    return status != timeBasedStatus &&
        status != ShowtimeStatus.cancelled &&
        status != ShowtimeStatus.full;
  }

  bool isSeatBooked(String seatId) => bookedSeats.contains(seatId);
  bool isSeatReserved(String seatId) => reservedSeats.contains(seatId);
  bool isSeatAvailable(String seatId) =>
      !isSeatBooked(seatId) && !isSeatReserved(seatId);

  bool isSeatReservedByUser(String seatId, String userId) {
    if (!isSeatReserved(seatId)) return false;
    final reservationInfo = reservationData[seatId];
    return reservationInfo != null && reservationInfo['userId'] == userId;
  }

  bool isSeatReservationExpired(String seatId) {
    if (!isSeatReserved(seatId)) return false;
    final reservationInfo = reservationData[seatId];
    if (reservationInfo == null) return true;

    final reservedAt = (reservationInfo['reservedAt'] as Timestamp).toDate();
    final now = DateTime.now();
    return now.difference(reservedAt).inMinutes >= 5;
  }

  Map<String, dynamic>? getSeatReservationInfo(String seatId) {
    return reservationData[seatId];
  }

  int get totalBookedSeats => bookedSeats.length;
  int get totalReservedSeats => reservedSeats.length;
  int get totalUnavailableSeats => totalBookedSeats + totalReservedSeats;

  List<SpecialOffer> get activeOffers => specialOffers;

  double getDiscountedPrice(String seatType, {String? offerType}) {
    double basePrice = pricing.getPriceForSeatType(seatType);

    if (offerType != null) {
      final offers = specialOffers.where((o) => o.type == offerType);
      final offer = offers.isNotEmpty ? offers.first : null;
      if (offer != null) {
        return basePrice * (1 - offer.discount / 100);
      }
    }

    return basePrice;
  }

  String get displayTime => '$time - $endTime';
  String get displayDate => date;
  String get displayDateTime => '$date $time';
}

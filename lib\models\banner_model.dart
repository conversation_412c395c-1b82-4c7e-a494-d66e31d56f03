import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';

enum BannerType {
  home,
  splash,
}

class BannerModel {
  final String id;
  final String imageUrl;
  final String title;
  final String? description;
  final BannerType type;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int? order;

  BannerModel({
    required this.id,
    required this.imageUrl,
    required this.title,
    this.description,
    required this.type,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.order,
  });

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    try {
      
      String imageUrl = '';
      if (json['imageUrl'] is String) {
        imageUrl = json['imageUrl'];
      } else if (json['imageUrl'] != null) {
        imageUrl = json['imageUrl'].toString();
      }

      
      String title = '';
      if (json['title'] is String) {
        title = json['title'];
      } else if (json['title'] != null) {
        title = json['title'].toString();
      }

      
      String typeStr = 'home';
      if (json['type'] is String) {
        typeStr = json['type'];
      } else if (json['type'] != null) {
        typeStr = json['type'].toString();
      }

      
      String? description;
      if (json['description'] is String) {
        description = json['description'];
      } else if (json['description'] != null) {
        description = json['description'].toString();
      }

      return BannerModel(
        id: json['id'] ?? '',
        imageUrl: imageUrl,
        title: title,
        description: description,
        type: _getBannerTypeFromString(typeStr),
        isActive: json['isActive'] ?? true,
        createdAt:
            (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        updatedAt:
            (json['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        order: json['order'] is int ? json['order'] : null,
      );
    } catch (e) {
      developer.log('Error parsing JSON: $e', name: 'BannerModel');
      
      return BannerModel(
        id: json['id'] ?? '',
        imageUrl: '',
        title: 'Error: Invalid data format',
        description: 'This banner has invalid data format: $e',
        type: BannerType.home,
        isActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  factory BannerModel.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;

      
      String imageUrl = '';
      if (data['imageUrl'] is String) {
        imageUrl = data['imageUrl'];
      } else if (data['imageUrl'] != null) {
        imageUrl = data['imageUrl'].toString();
      }

      
      String title = '';
      if (data['title'] is String) {
        title = data['title'];
      } else if (data['title'] != null) {
        title = data['title'].toString();
      }

      
      String typeStr = 'home';
      if (data['type'] is String) {
        typeStr = data['type'];
      } else if (data['type'] != null) {
        typeStr = data['type'].toString();
      }

      
      String? description;
      if (data['description'] is String) {
        description = data['description'];
      } else if (data['description'] != null) {
        description = data['description'].toString();
      }

      return BannerModel(
        id: doc.id,
        imageUrl: imageUrl,
        title: title,
        description: description,
        type: _getBannerTypeFromString(typeStr),
        isActive: data['isActive'] ?? true,
        createdAt:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        updatedAt:
            (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        order: data['order'] is int ? data['order'] : null,
      );
    } catch (e) {
      developer.log('Error parsing document ${doc.id}: $e',
          name: 'BannerModel');
      
      return BannerModel(
        id: doc.id,
        imageUrl: '',
        title: 'Error: Invalid data format',
        description: 'This banner has invalid data format: $e',
        type: BannerType.home,
        isActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'imageUrl': imageUrl,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'order': order,
    };
  }

  BannerModel copyWith({
    String? id,
    String? imageUrl,
    String? title,
    String? description,
    BannerType? type,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? order,
  }) {
    return BannerModel(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      order: order ?? this.order,
    );
  }

  static BannerType _getBannerTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'splash':
        return BannerType.splash;
      case 'home':
      default:
        return BannerType.home;
    }
  }
}

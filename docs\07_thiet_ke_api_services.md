

<PERSON><PERSON> thống "Đớp Phim" sử dụng hybrid API architecture kết hợp Firebase services với external APIs để cung cấp functionality toàn diện. <PERSON>ến trúc được thiết kế theo RESTful principles với real-time capabilities.

**[CHÈN ẢNH: API Architecture Overview - Hình 7.1]**
*S<PERSON> đồ tổng quan kiến trúc API và service integration*

**Internal APIs (Firebase):**
- Cloud Functions cho server-side logic
- Firestore REST API cho data operations
- Realtime Database API cho live updates
- Firebase Auth API cho authentication

**External APIs:**
- TMDB API cho movie metadata
- PayPal API cho payment processing
- FCM API cho push notifications
- Google Maps API cho location services (future)

**API Design Principles:**
- RESTful design patterns
- Consistent response formats
- Proper HTTP status codes
- Rate limiting và throttling
- Comprehensive error handling
- API versioning strategy

**Deployment Structure:**
```
functions/
├── src/
│   ├── auth/           # Authentication functions
│   ├── booking/        # Booking business logic
│   ├── payment/        # Payment processing
│   ├── notification/   # Notification services
│   ├── admin/          # Admin operations
│   └── utils/          # Shared utilities
├── package.json
└── firebase.json
```

**Environment Configuration:**
```javascript

const config = {
  development: {
    paypal: {
      clientId: process.env.PAYPAL_SANDBOX_CLIENT_ID,
      clientSecret: process.env.PAYPAL_SANDBOX_SECRET,
      baseUrl: 'https://api.sandbox.paypal.com'
    },
    tmdb: {
      apiKey: process.env.TMDB_API_KEY,
      baseUrl: 'https://api.themoviedb.org/3'
    }
  },
  production: {
    paypal: {
      clientId: process.env.PAYPAL_LIVE_CLIENT_ID,
      clientSecret: process.env.PAYPAL_LIVE_SECRET,
      baseUrl: 'https://api.paypal.com'
    }
  }
};
```

**User Registration Function:**
```javascript

exports.registerUser = functions.https.onCall(async (data, context) => {
  try {

    const { email, password, name } = data;
    if (!email || !password || !name) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }

    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      displayName: name,
      emailVerified: false
    });

    await admin.firestore().collection('users').doc(userRecord.uid).set({
      id: userRecord.uid,
      email: email,
      name: name,
      role: 'user',
      photoUrl: generateAvatarUrl(name),
      preferences: {
        language: 'vi',
        notifications: {
          booking: true,
          promotions: true,
          reminders: true
        }
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true
    });

    await sendVerificationEmail(userRecord.uid, email);

    return {
      success: true,
      userId: userRecord.uid,
      message: 'User registered successfully'
    };

  } catch (error) {
    console.error('Registration error:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Registration failed',
      error.message
    );
  }
});
```

**Role Assignment Function:**
```javascript

exports.assignUserRole = functions.https.onCall(async (data, context) => {

  if (!context.auth || !await isAdmin(context.auth.uid)) {
    throw new functions.https.HttpsError(
      'permission-denied',
      'Only admins can assign roles'
    );
  }

  const { userId, role } = data;

  await admin.auth().setCustomUserClaims(userId, { role });

  await admin.firestore().collection('users').doc(userId).update({
    role: role,
    updatedAt: admin.firestore.FieldValue.serverTimestamp()
  });

  return { success: true, message: `Role ${role} assigned to user` };
});
```

**[CHÈN ẢNH: Cloud Functions Console - Hình 7.2]**
*Screenshot của Firebase Cloud Functions console*

**Seat Reservation Function:**
```javascript

exports.reserveSeat = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { showtimeId, seatIds } = data;
  const userId = context.auth.uid;
  const reservationTime = admin.firestore.Timestamp.now();
  const expirationTime = new admin.firestore.Timestamp(
    reservationTime.seconds + (10 * 60), // 10 minutes
    reservationTime.nanoseconds
  );

  const batch = admin.firestore().batch();

  try {

    const showtimeRef = admin.firestore().collection('showtimes').doc(showtimeId);
    const showtimeDoc = await showtimeRef.get();
    
    if (!showtimeDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Showtime not found');
    }

    const showtimeData = showtimeDoc.data();
    const bookedSeats = showtimeData.bookedSeats || [];
    const reservedSeats = showtimeData.reservedSeats || [];

    for (const seatId of seatIds) {
      if (bookedSeats.includes(seatId)) {
        throw new functions.https.HttpsError('already-exists', `Seat ${seatId} is already booked`);
      }
      
      const existingReservation = reservedSeats.find(r => 
        r.seatId === seatId && 
        r.expiresAt.toDate() > new Date()
      );
      
      if (existingReservation && existingReservation.userId !== userId) {
        throw new functions.https.HttpsError('already-exists', `Seat ${seatId} is temporarily reserved`);
      }
    }

    const newReservations = seatIds.map(seatId => ({
      seatId,
      userId,
      reservedAt: reservationTime,
      expiresAt: expirationTime
    }));

    const updatedReservations = reservedSeats
      .filter(r => r.userId !== userId) // Remove user's old reservations
      .concat(newReservations);

    batch.update(showtimeRef, {
      reservedSeats: updatedReservations,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    const realtimeUpdates = {};
    seatIds.forEach(seatId => {
      realtimeUpdates[`seat_reservations/${showtimeId}/${seatId}`] = {
        userId,
        reservedAt: reservationTime.toDate().toISOString(),
        expiresAt: expirationTime.toDate().toISOString(),
        status: 'reserved'
      };
    });

    await admin.database().ref().update(realtimeUpdates);
    await batch.commit();

    await scheduleReservationCleanup(showtimeId, seatIds, expirationTime);

    return {
      success: true,
      reservationId: `${showtimeId}_${userId}_${Date.now()}`,
      expiresAt: expirationTime.toDate().toISOString(),
      message: 'Seats reserved successfully'
    };

  } catch (error) {
    console.error('Seat reservation error:', error);
    throw error;
  }
});
```

**PayPal Payment Processing:**
```javascript

exports.processPayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { ticketId, paypalOrderId } = data;
  const userId = context.auth.uid;

  try {

    const paypalVerification = await verifyPayPalPayment(paypalOrderId);
    
    if (!paypalVerification.success) {
      throw new functions.https.HttpsError('payment-failed', 'PayPal payment verification failed');
    }

    const ticketRef = admin.firestore().collection('tickets').doc(ticketId);
    const ticketDoc = await ticketRef.get();
    
    if (!ticketDoc.exists || ticketDoc.data().userId !== userId) {
      throw new functions.https.HttpsError('not-found', 'Ticket not found or access denied');
    }

    const ticketData = ticketDoc.data();

    const paymentData = {
      id: `payment_${Date.now()}`,
      userId: userId,
      ticketId: ticketId,
      amount: paypalVerification.amount,
      currency: paypalVerification.currency,
      method: 'paypal',
      status: 'completed',
      gatewayTransactionId: paypalOrderId,
      gatewayResponse: paypalVerification.details,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      completedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    const batch = admin.firestore().batch();

    batch.update(ticketRef, {
      status: 'confirmed',
      paymentId: paymentData.id,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    const paymentRef = admin.firestore().collection('payments').doc(paymentData.id);
    batch.set(paymentRef, paymentData);

    const showtimeRef = admin.firestore().collection('showtimes').doc(ticketData.showtimeId);
    const showtimeDoc = await showtimeRef.get();
    const showtimeData = showtimeDoc.data();
    
    const updatedBookedSeats = [...(showtimeData.bookedSeats || []), ...ticketData.selectedSeats];
    const updatedReservedSeats = (showtimeData.reservedSeats || [])
      .filter(r => r.userId !== userId);

    batch.update(showtimeRef, {
      bookedSeats: updatedBookedSeats,
      reservedSeats: updatedReservedSeats,
      availableSeats: showtimeData.totalSeats - updatedBookedSeats.length,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    await batch.commit();

    await sendBookingConfirmation(userId, ticketData);

    const realtimeUpdates = {};
    ticketData.selectedSeats.forEach(seatId => {
      realtimeUpdates[`seat_reservations/${ticketData.showtimeId}/${seatId}`] = null;
    });
    await admin.database().ref().update(realtimeUpdates);

    return {
      success: true,
      paymentId: paymentData.id,
      ticketStatus: 'confirmed',
      message: 'Payment processed successfully'
    };

  } catch (error) {
    console.error('Payment processing error:', error);

    await createFailedPaymentRecord(userId, ticketId, paypalOrderId, error.message);
    
    throw new functions.https.HttpsError('payment-failed', 'Payment processing failed', error.message);
  }
});

async function verifyPayPalPayment(orderId) {
  const paypalConfig = getPayPalConfig();
  
  try {

    const tokenResponse = await fetch(`${paypalConfig.baseUrl}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${paypalConfig.clientId}:${paypalConfig.clientSecret}`).toString('base64')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: 'grant_type=client_credentials'
    });

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    const orderResponse = await fetch(`${paypalConfig.baseUrl}/v2/checkout/orders/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    const orderData = await orderResponse.json();

    if (orderData.status === 'COMPLETED') {
      return {
        success: true,
        amount: parseFloat(orderData.purchase_units[0].amount.value),
        currency: orderData.purchase_units[0].amount.currency_code,
        details: orderData
      };
    }

    return { success: false, error: 'Payment not completed' };

  } catch (error) {
    console.error('PayPal verification error:', error);
    return { success: false, error: error.message };
  }
}
```

**[CHÈN ẢNH: PayPal Integration Flow - Hình 7.3]**
*Sơ đồ luồng tích hợp PayPal payment*

**Movie Data Service:**
```dart

class TMDBService {
  static const String baseUrl = 'https://api.themoviedb.org/3';
  static const String apiKey = 'your_tmdb_api_key';
  static const String imageBaseUrl = 'https://image.tmdb.org/t/p/w500';

  static Future<List<Movie>> getPopularMovies({int page = 1}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/movie/popular?api_key=$apiKey&page=$page&language=vi-VN'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'];
        
        return results.map((json) => Movie.fromTMDB(json)).toList();
      } else {
        throw TMDBException('Failed to fetch popular movies: ${response.statusCode}');
      }
    } catch (e) {
      throw TMDBException('Network error: $e');
    }
  }

  static Future<MovieDetail> getMovieDetails(int movieId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/movie/$movieId?api_key=$apiKey&language=vi-VN&append_to_response=credits,videos'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MovieDetail.fromTMDB(data);
      } else {
        throw TMDBException('Failed to fetch movie details: ${response.statusCode}');
      }
    } catch (e) {
      throw TMDBException('Network error: $e');
    }
  }

  static Future<List<Movie>> searchMovies(String query, {int page = 1}) async {
    try {
      final encodedQuery = Uri.encodeComponent(query);
      final response = await http.get(
        Uri.parse('$baseUrl/search/movie?api_key=$apiKey&query=$encodedQuery&page=$page&language=vi-VN'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> results = data['results'];
        
        return results.map((json) => Movie.fromTMDB(json)).toList();
      } else {
        throw TMDBException('Failed to search movies: ${response.statusCode}');
      }
    } catch (e) {
      throw TMDBException('Network error: $e');
    }
  }
}

class TMDBException implements Exception {
  final String message;
  TMDBException(this.message);
  
  @override
  String toString() => 'TMDBException: $message';
}
```

**PayPal Service:**
```dart

class PayPalService {
  static const String sandboxUrl = 'https://api.sandbox.paypal.com';
  static const String liveUrl = 'https://api.paypal.com';
  
  static String get baseUrl => kDebugMode ? sandboxUrl : liveUrl;
  static String get clientId => kDebugMode 
      ? 'your_sandbox_client_id' 
      : 'your_live_client_id';

  static Future<PayPalPayment> createPayment({
    required double amount,
    required String currency,
    required String description,
    required Map<String, dynamic> metadata,
  }) async {
    try {

      final paypalAmount = (amount / 23000).toStringAsFixed(2); // VND to USD conversion
      
      final orderData = {
        'intent': 'CAPTURE',
        'purchase_units': [
          {
            'amount': {
              'currency_code': 'USD',
              'value': paypalAmount,
            },
            'description': description,
            'custom_id': metadata['ticketId'],
          }
        ],
        'application_context': {
          'return_url': 'https://dopphim.app/payment/success',
          'cancel_url': 'https://dopphim.app/payment/cancel',
          'brand_name': 'Đớp Phim',
          'user_action': 'PAY_NOW',
        }
      };

      final result = await FirebaseFunctions.instance
          .httpsCallable('createPayPalOrder')
          .call(orderData);

      if (result.data['success']) {
        return PayPalPayment(
          orderId: result.data['orderId'],
          approvalUrl: result.data['approvalUrl'],
          amount: amount,
          currency: currency,
        );
      } else {
        throw PayPalException(result.data['error']);
      }
    } catch (e) {
      throw PayPalException('Failed to create PayPal payment: $e');
    }
  }

  static Future<bool> capturePayment(String orderId) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('capturePayPalPayment')
          .call({'orderId': orderId});

      return result.data['success'] == true;
    } catch (e) {
      throw PayPalException('Failed to capture PayPal payment: $e');
    }
  }
}

class PayPalPayment {
  final String orderId;
  final String approvalUrl;
  final double amount;
  final String currency;

  PayPalPayment({
    required this.orderId,
    required this.approvalUrl,
    required this.amount,
    required this.currency,
  });
}

class PayPalException implements Exception {
  final String message;
  PayPalException(this.message);
  
  @override
  String toString() => 'PayPalException: $message';
}
```

**[CHÈN ẢNH: API Integration Dashboard - Hình 7.4]**
*Dashboard hiển thị status của các external API integrations*

**API Error Response Format:**
```javascript

const createErrorResponse = (code, message, details = null) => ({
  success: false,
  error: {
    code: code,
    message: message,
    details: details,
    timestamp: new Date().toISOString(),
    requestId: generateRequestId()
  }
});

const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR'
};
```

**Client-side Error Handling:**
```dart

class ApiService {
  static Future<T> handleApiCall<T>(Future<T> Function() apiCall) async {
    try {
      return await apiCall();
    } on FirebaseFunctionsException catch (e) {
      throw ApiException.fromFirebaseException(e);
    } on SocketException catch (e) {
      throw ApiException(
        code: 'NETWORK_ERROR',
        message: 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.',
        details: e.toString(),
      );
    } on TimeoutException catch (e) {
      throw ApiException(
        code: 'TIMEOUT_ERROR',
        message: 'Yêu cầu quá thời gian chờ. Vui lòng thử lại.',
        details: e.toString(),
      );
    } catch (e) {
      throw ApiException(
        code: 'UNKNOWN_ERROR',
        message: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
        details: e.toString(),
      );
    }
  }
}

class ApiException implements Exception {
  final String code;
  final String message;
  final String? details;

  ApiException({
    required this.code,
    required this.message,
    this.details,
  });

  factory ApiException.fromFirebaseException(FirebaseFunctionsException e) {
    return ApiException(
      code: e.code,
      message: e.message ?? 'Đã xảy ra lỗi từ server',
      details: e.details?.toString(),
    );
  }

  @override
  String toString() => 'ApiException($code): $message';
}
```

**Performance Monitoring:**
```javascript

const { performance } = require('perf_hooks');

const monitorFunction = (functionName) => {
  return (target, propertyKey, descriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = performance.now();
      const requestId = generateRequestId();
      
      console.log(`[${requestId}] Starting ${functionName}`);
      
      try {
        const result = await originalMethod.apply(this, args);
        const endTime = performance.now();
        const duration = endTime - startTime;

        await logMetrics({
          functionName,
          requestId,
          duration,
          status: 'success',
          timestamp: new Date().toISOString()
        });
        
        console.log(`[${requestId}] Completed ${functionName} in ${duration.toFixed(2)}ms`);
        return result;
        
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        await logMetrics({
          functionName,
          requestId,
          duration,
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
        
        console.error(`[${requestId}] Failed ${functionName} after ${duration.toFixed(2)}ms:`, error);
        throw error;
      }
    };
    
    return descriptor;
  };
};

class BookingService {
  @monitorFunction('reserveSeat')
  async reserveSeat(data, context) {

  }
}
```

**[CHÈN ẢNH: API Monitoring Dashboard - Hình 7.5]**
*Real-time dashboard hiển thị API performance metrics*

---

*Phần tiếp theo: 8. Thiết kế giao diện người dùng*

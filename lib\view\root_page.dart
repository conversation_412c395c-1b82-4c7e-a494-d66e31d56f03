

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../utils/app_colors.dart';
import 'page/account_page.dart';
import 'page/favorite_page.dart';
import 'page/home_page.dart';
import 'page/notification_page.dart';
import 'page/ticket_page.dart';

class RootPage extends StatelessWidget {
  RootPage({Key? key, required this.i}) : super(key: key);

  final int i; 

  
  
  final pages = [
    const HomePage(), 
    const FavoritePage(), 
    const TicketPage(), 
    const AccountPage(), 
    const NotificationPage(
        showBackButton: false) 
  ];

  final index = 0.obs; 

  @override
  Widget build(BuildContext context) {
    index.value = i;
    return Scaffold(
      extendBody: true,
      bottomNavigationBar: NavigationBarTheme(
        data: NavigationBarThemeData(
          indicatorColor: Colors.white.withOpacity(0.2),
          labelTextStyle: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return GoogleFonts.mulish(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              );
            }
            return GoogleFonts.mulish(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: Colors.white70,
            );
          }),
        ),
        child: Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primaryGradientStart.withOpacity(0.95),
                AppColors.primaryGradientEnd.withOpacity(0.98),
              ],
              stops: const [0.0, 1.0],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 25,
                offset: const Offset(0, -8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: AppColors.primaryGradientStart.withOpacity(0.2),
                blurRadius: 15,
                offset: const Offset(0, -3),
                spreadRadius: 0,
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Obx(
                () => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildNavItem(
                      index: 0,
                      activeIcon: Icons.home_rounded,
                      inactiveIcon: Icons.home_outlined,
                      label: 'Trang chủ',
                      currentIndex: index.value,
                      onTap: () => _onNavTap(0),
                    ),
                    _buildNavItem(
                      index: 1,
                      activeIcon: Icons.favorite_rounded,
                      inactiveIcon: Icons.favorite_border_rounded,
                      label: 'Yêu thích',
                      currentIndex: index.value,
                      onTap: () => _onNavTap(1),
                    ),
                    _buildNavItem(
                      index: 2,
                      activeIcon: Icons.confirmation_number_rounded,
                      inactiveIcon: Icons.confirmation_number_outlined,
                      label: 'Vé của tôi',
                      currentIndex: index.value,
                      onTap: () => _onNavTap(2),
                    ),
                    _buildNavItem(
                      index: 3,
                      activeIcon: Icons.person_rounded,
                      inactiveIcon: Icons.person_outline_rounded,
                      label: 'Tài khoản',
                      currentIndex: index.value,
                      onTap: () => _onNavTap(3),
                    ),
                    _buildNavItem(
                      index: 4,
                      activeIcon: Icons.notifications_rounded,
                      inactiveIcon: Icons.notifications_outlined,
                      label: 'Thông báo',
                      currentIndex: index.value,
                      onTap: () => _onNavTap(4),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: Obx(
          () => pages[index.value],
        ),
      ),
    );
  }

  void _onNavTap(int selectedIndex) {
    
    if (selectedIndex != index.value) {
      
    }
    index.value = selectedIndex;
  }

  Widget _buildNavItem({
    required int index,
    required IconData activeIcon,
    required IconData inactiveIcon,
    required String label,
    required int currentIndex,
    required VoidCallback onTap,
  }) {
    final isSelected = currentIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                width: isSelected ? 42 : 36,
                height: isSelected ? 42 : 36,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withOpacity(0.2)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(21),
                  border: isSelected
                      ? Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        )
                      : null,
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 0,
                          ),
                        ]
                      : null,
                ),
                child: AnimatedScale(
                  duration: const Duration(milliseconds: 200),
                  scale: isSelected ? 1.1 : 1.0,
                  child: Icon(
                    isSelected ? activeIcon : inactiveIcon,
                    color: isSelected ? Colors.white : Colors.white70,
                    size: isSelected ? 24 : 20,
                  ),
                ),
              ),

              const SizedBox(height: 3),

              
              AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: isSelected ? 1.0 : 0.7,
                child: AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 300),
                  style: GoogleFonts.mulish(
                    fontSize: isSelected ? 10 : 9,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.white70,
                  ),
                  child: Text(
                    label,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(top: 1),
                width: isSelected ? 4 : 0,
                height: isSelected ? 4 : 0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.white.withOpacity(0.5),
                            blurRadius: 4,
                            spreadRadius: 0,
                          ),
                        ]
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/screen_model.dart';

class ScreenService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'screens';

  
  Future<List<ScreenModel>> getAllScreens({bool activeOnly = false}) async {
    try {
      Query query = _firestore.collection(_collection);

      if (activeOnly) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.orderBy('name').get();

      return snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get screens: $e');
    }
  }

  
  Future<List<ScreenModel>> getScreensByTheater(String theaterId,
      {bool activeOnly = false}) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .where('theaterId', isEqualTo: theaterId);

      if (activeOnly) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.orderBy('name').get();

      return snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get screens by theater: $e');
    }
  }

  
  Future<ScreenModel?> getScreenById(String screenId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(screenId).get();

      if (doc.exists) {
        return ScreenModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get screen: $e');
    }
  }

  
  Future<List<ScreenModel>> getScreensByType(ScreenType type) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('type', isEqualTo: type.name)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get screens by type: $e');
    }
  }

  
  Future<List<ScreenModel>> getScreensWithAmenities(
      List<String> amenities) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('amenities', arrayContainsAny: amenities)
          .get();

      return snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get screens with amenities: $e');
    }
  }

  
  Future<List<ScreenModel>> searchScreens(String query) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final screens =
          snapshot.docs.map((doc) => ScreenModel.fromFirestore(doc)).toList();

      
      return screens.where((screen) {
        final name = screen.name.toLowerCase();
        final type = screen.type.displayName.toLowerCase();
        final searchQuery = query.toLowerCase();

        return name.contains(searchQuery) || type.contains(searchQuery);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search screens: $e');
    }
  }

  
  Future<List<ScreenModel>> getScreensWithMinCapacity(int minSeats) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('totalSeats', isGreaterThanOrEqualTo: minSeats)
          .orderBy('totalSeats')
          .get();

      return snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get screens with min capacity: $e');
    }
  }

  
  Future<ScreenModel> addScreen(ScreenModel screen) async {
    try {
      final now = DateTime.now();
      final screenData = screen.copyWith(
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _firestore
          .collection(_collection)
          .add(screenData.toFirestore());

      return screenData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add screen: $e');
    }
  }

  Future<void> updateScreen(ScreenModel screen) async {
    try {
      final updatedScreen = screen.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_collection)
          .doc(screen.id)
          .update(updatedScreen.toFirestore());
    } catch (e) {
      throw Exception('Failed to update screen: $e');
    }
  }

  Future<void> deleteScreen(String screenId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(screenId)
          .update({'isActive': false, 'updatedAt': Timestamp.now()});
    } catch (e) {
      throw Exception('Failed to delete screen: $e');
    }
  }

  
  Stream<List<ScreenModel>> getScreensStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ScreenModel.fromFirestore(doc))
            .toList());
  }

  Stream<List<ScreenModel>> getScreensByTheaterStream(String theaterId) {
    return _firestore
        .collection(_collection)
        .where('theaterId', isEqualTo: theaterId)
        .where('isActive', isEqualTo: true)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ScreenModel.fromFirestore(doc))
            .toList());
  }

  Stream<ScreenModel?> getScreenStream(String screenId) {
    return _firestore
        .collection(_collection)
        .doc(screenId)
        .snapshots()
        .map((doc) => doc.exists ? ScreenModel.fromFirestore(doc) : null);
  }

  
  Future<List<ScreenType>> getAvailableScreenTypes() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final types = snapshot.docs
          .map((doc) => ScreenModel.fromFirestore(doc).type)
          .toSet()
          .toList();

      return types;
    } catch (e) {
      throw Exception('Failed to get available screen types: $e');
    }
  }

  
  Future<List<String>> getAvailableAmenities() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final amenities = <String>{};
      for (final doc in snapshot.docs) {
        final screen = ScreenModel.fromFirestore(doc);
        amenities.addAll(screen.amenities);
      }

      final amenitiesList = amenities.toList();
      amenitiesList.sort();
      return amenitiesList;
    } catch (e) {
      throw Exception('Failed to get available amenities: $e');
    }
  }

  
  Future<Map<String, dynamic>> getScreenStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();

      int totalScreens = 0;
      int activeScreens = 0;
      int totalSeats = 0;
      final typeCounts = <String, int>{};
      final amenityCounts = <String, int>{};
      final theaterCounts = <String, int>{};

      for (final doc in snapshot.docs) {
        final screen = ScreenModel.fromFirestore(doc);
        totalScreens++;

        if (screen.isActive) {
          activeScreens++;
          totalSeats += screen.totalSeats;

          
          final type = screen.type.displayName;
          typeCounts[type] = (typeCounts[type] ?? 0) + 1;

          
          for (final amenity in screen.amenities) {
            amenityCounts[amenity] = (amenityCounts[amenity] ?? 0) + 1;
          }

          
          theaterCounts[screen.theaterId] =
              (theaterCounts[screen.theaterId] ?? 0) + 1;
        }
      }

      return {
        'totalScreens': totalScreens,
        'activeScreens': activeScreens,
        'inactiveScreens': totalScreens - activeScreens,
        'totalSeats': totalSeats,
        'averageSeatsPerScreen':
            activeScreens > 0 ? totalSeats / activeScreens : 0,
        'typeCounts': typeCounts,
        'amenityCounts': amenityCounts,
        'theaterCounts': theaterCounts,
      };
    } catch (e) {
      throw Exception('Failed to get screen statistics: $e');
    }
  }

  
  Future<void> updateSeatAvailability(String screenId, String rowLetter,
      String seatNumber, bool isAvailable) async {
    try {
      final screen = await getScreenById(screenId);
      if (screen == null) throw Exception('Screen not found');

      final updatedSeatLayout = screen.seatLayout.map((row) {
        if (row.row == rowLetter) {
          final updatedSeats = row.seats.map((seat) {
            if (seat.number == seatNumber) {
              return seat.copyWith(isAvailable: isAvailable);
            }
            return seat;
          }).toList();
          return row.copyWith(seats: updatedSeats);
        }
        return row;
      }).toList();

      final updatedScreen = screen.copyWith(
        seatLayout: updatedSeatLayout,
        updatedAt: DateTime.now(),
      );

      await updateScreen(updatedScreen);
    } catch (e) {
      throw Exception('Failed to update seat availability: $e');
    }
  }

  
  Future<Map<String, int>> fixScreenCapacity() async {
    try {
      print('Starting screen capacity fix...');

      int fixedScreens = 0;
      int issuesFound = 0;

      
      final screensSnapshot = await _firestore.collection(_collection).get();

      for (final screenDoc in screensSnapshot.docs) {
        try {
          final screenData = screenDoc.data();
          final screenId = screenDoc.id;
          final currentTotalSeats = screenData['totalSeats'] as int? ?? 0;
          final seatLayout = screenData['seatLayout'] as List<dynamic>? ?? [];

          
          int actualTotalSeats = 0;
          for (final row in seatLayout) {
            if (row is Map<String, dynamic>) {
              final seats = row['seats'] as List<dynamic>? ?? [];
              actualTotalSeats += seats.length;
            }
          }

          
          if (currentTotalSeats != actualTotalSeats && actualTotalSeats > 0) {
            print(
                'Screen $screenId: Fixing totalSeats from $currentTotalSeats to $actualTotalSeats');

            
            await _firestore.collection(_collection).doc(screenId).update({
              'totalSeats': actualTotalSeats,
              'updatedAt': Timestamp.now(),
            });

            issuesFound++;
          }

          fixedScreens++;
        } catch (e) {
          print('Error processing screen ${screenDoc.id}: $e');
        }
      }

      print(
          'Screen capacity fix completed: $fixedScreens screens processed, $issuesFound issues fixed');

      return {
        'fixed': fixedScreens,
        'issues': issuesFound,
      };
    } catch (e) {
      print('Error during screen capacity fix: $e');
      throw Exception('Failed to fix screen capacity: $e');
    }
  }
}

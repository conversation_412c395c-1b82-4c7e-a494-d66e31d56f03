import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';
import 'movie_edit_page.dart';
import 'import_data_page.dart';

class MovieManagementPage extends StatefulWidget {
  const MovieManagementPage({Key? key}) : super(key: key);

  @override
  State<MovieManagementPage> createState() => _MovieManagementPageState();
}

class _MovieManagementPageState extends State<MovieManagementPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final TextEditingController _searchController = TextEditingController();
  final RxString _searchText = ''.obs;
  MovieStatus? _selectedStatus;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _movieController.fetchAllFirebaseMovies();
    });

    
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    
    _searchText.value = _searchController.text;

    
    _debounceTimer?.cancel();

    
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _movieController.searchFirebaseMovies(_searchController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff2B5876),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản Lý Phim',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _showImportMenu(),
                      icon: const Icon(Icons.upload_file, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: () => _navigateToEditPage(null),
                      icon: const Icon(Icons.add, color: Colors.white),
                    ),
                  ],
                ),
              ),

              
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _searchController,
                  style: GoogleFonts.mulish(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm phim...',
                    hintStyle: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.6),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Colors.white.withOpacity(0.6),
                    ),
                    suffixIcon: Obx(() {
                      if (_movieController.isLoadingSearch.value) {
                        return Container(
                          padding: const EdgeInsets.all(12),
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white.withOpacity(0.6),
                              ),
                            ),
                          ),
                        );
                      } else if (_searchText.value.isNotEmpty) {
                        return IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _searchText.value = '';
                            _movieController.clearSearch();
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Colors.white.withOpacity(0.6),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    }),
                    filled: true,
                    fillColor: Colors.white.withOpacity(0.1),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              
              Obx(() {
                if (_searchText.value.isNotEmpty &&
                    !_movieController.isLoadingSearch.value) {
                  final resultCount = _movieController.searchResults.length;
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Tìm thấy $resultCount kết quả cho "${_searchText.value}"',
                      style: GoogleFonts.mulish(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),

              
              Obx(() {
                if (_searchText.value.isNotEmpty) {
                  return const SizedBox.shrink();
                }
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  height: 50,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildFilterChip('Tất cả', null),
                      const SizedBox(width: 8),
                      _buildFilterChip('Đang chiếu', MovieStatus.nowPlaying),
                      const SizedBox(width: 8),
                      _buildFilterChip('Sắp chiếu', MovieStatus.upcoming),
                      const SizedBox(width: 8),
                      _buildFilterChip('Đã kết thúc', MovieStatus.ended),
                    ],
                  ),
                );
              }),

              const SizedBox(height: 16),

              
              Expanded(
                child: Obx(() {
                  if (_movieController.isLoadingFirebaseMovies.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  final movies = _getFilteredMovies();

                  if (movies.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _searchText.value.isNotEmpty
                                ? Icons.search_off
                                : Icons.movie_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchText.value.isNotEmpty
                                ? 'Không tìm thấy phim nào với từ khóa "${_searchText.value}"'
                                : 'Chưa có phim nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (_searchText.value.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                _searchController.clear();
                                _searchText.value = '';
                                _movieController.clearSearch();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white.withOpacity(0.1),
                                foregroundColor: Colors.white,
                              ),
                              child: Text(
                                'Xóa tìm kiếm',
                                style: GoogleFonts.mulish(),
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => _movieController.fetchAllFirebaseMovies(),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: movies.length,
                      itemBuilder: (context, index) {
                        final movie = movies[index];
                        return _buildMovieCard(movie);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, MovieStatus? status) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.mulish(
          color: isSelected ? Colors.white : Colors.white70,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
      },
      backgroundColor: Colors.white.withOpacity(0.1),
      selectedColor: Colors.amber.withOpacity(0.3),
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected ? Colors.amber : Colors.white30,
      ),
    );
  }

  List<Movie> _getFilteredMovies() {
    
    if (_searchText.value.isNotEmpty) {
      return _movieController.searchResults;
    }

    
    if (_selectedStatus == null) {
      return _movieController.allMovies;
    } else if (_selectedStatus == MovieStatus.nowPlaying) {
      return _movieController.nowPlayingMovies;
    } else if (_selectedStatus == MovieStatus.upcoming) {
      return _movieController.upcomingFirebaseMovies;
    } else {
      return _movieController.endedMovies;
    }
  }

  Widget _buildMovieCard(Movie movie) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: Colors.white.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                movie.fullPosterPath,
                width: 60,
                height: 90,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 60,
                    height: 90,
                    color: Colors.grey[300],
                    child: const Icon(Icons.movie, color: Colors.grey),
                  );
                },
              ),
            ),
            const SizedBox(width: 16),

            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    movie.title,
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    movie.displayStatus,
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      color: _getStatusColor(movie.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      if (movie.isHomeBanner)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Home',
                            style: GoogleFonts.mulish(
                              fontSize: 10,
                              color: Colors.blue[200],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      if (movie.isHomeBanner && movie.isSplashBanner)
                        const SizedBox(width: 4),
                      if (movie.isSplashBanner)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.purple.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Splash',
                            style: GoogleFonts.mulish(
                              fontSize: 10,
                              color: Colors.purple[200],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),

            
            Column(
              children: [
                IconButton(
                  onPressed: () => _navigateToEditPage(movie),
                  icon: const Icon(Icons.edit, color: Colors.white70),
                ),
                IconButton(
                  onPressed: () => _showDeleteDialog(movie),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(MovieStatus status) {
    switch (status) {
      case MovieStatus.nowPlaying:
        return Colors.green;
      case MovieStatus.upcoming:
        return Colors.orange;
      case MovieStatus.ended:
        return Colors.grey;
    }
  }

  void _navigateToEditPage(Movie? movie) async {
    final result = await Get.to(() => MovieEditPage(movie: movie));
    if (result == true) {
      _movieController.fetchAllFirebaseMovies();
    }
  }

  void _showDeleteDialog(Movie movie) {
    Get.dialog(
      AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc chắn muốn xóa phim "${movie.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success =
                  await _movieController.deleteMovie(movie.id.toString());
              if (success) {
                Get.snackbar(
                  'Thành công',
                  'Phim đã được xóa',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text('Xóa', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showImportMenu() {
    Get.to(() => const ImportDataPage(dataType: ImportDataType.movie))
        ?.then((_) {
      
      _movieController.fetchAllFirebaseMovies();
    });
  }
}

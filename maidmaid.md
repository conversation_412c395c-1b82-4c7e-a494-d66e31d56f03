```mermaid
flowchart LR
    %% Start and Auth
    Start([👤 Bắt đầu đặt vé]) --> Auth{🔐 Đã đăng nhập?}
    Auth -->|Chưa| Login[🔑 Đăng nhập]
    Auth -->|R<PERSON>i| Step1
    Login --> Step1

    %% 5 Main Steps - Horizontal flow
    Step1[🎬 BƯỚC 1<br/>Chọn Phim] --> Step2[🏢 BƯỚC 2<br/>Chọn Rạp]
    Step2 --> Step3[🕐 BƯỚC 3<br/>Chọn Suất Chiếu]
    Step3 --> Step4[💺 BƯỚC 4<br/>Chọn Ghế]
    Step4 --> Step5[💳 BƯỚC 5<br/>Thanh Toán]

    %% Validation checks
    Step1 --> Check1{Có lịch chiếu?}
    Check1 -->|Không| Error1[❌ Chưa có lịch chiếu]
    Check1 -->|Có| Step2

    Step2 --> Check2{Rạp hoạt động?}
    Check2 -->|Không| Error2[❌ Rạp tạm ngừng]
    Check2 -->|Có| Step3

    Step3 --> Check3{Còn vé?}
    Check3 -->|Hết| Error3[❌ Hết vé]
    Check3 -->|Còn| Step4

    Step4 --> Reserve[⏰ Giữ ghế 10 phút]
    Reserve --> Check4{Ghế available?}
    Check4 -->|Không| Error4[❌ Ghế đã đặt]
    Check4 -->|Có| Step5

    %% Payment processing
    Step5 --> Payment[💰 Xử lý PayPal]
    Payment --> PayResult{Kết quả?}
    PayResult -->|Thành công| Success[✅ Thành công]
    PayResult -->|Thất bại| PayError[❌ Lỗi thanh toán]

    %% Success flow
    Success --> CreateTicket[🎫 Tạo vé điện tử]
    CreateTicket --> Notify[📱 Gửi thông báo]
    Notify --> Complete[🎉 Hoàn thành]

    %% Error handling
    Error1 --> Step1
    Error2 --> Step2
    Error3 --> Step3
    Error4 --> Step4
    PayError --> Retry{Thử lại?}
    Retry -->|Có| Payment
    Retry -->|Không| Cancel[❌ Hủy đặt vé]

    %% Timeout handling
    Reserve -.->|10 phút| Timeout[⏰ Hết thời gian]
    Timeout --> Cancel
    Cancel --> Release[🔄 Giải phóng ghế]
    Release --> Step1

    %% Styling for A4 landscape
    classDef stepClass fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,font-size:10px
    classDef checkClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px,font-size:9px
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,font-size:9px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px,font-size:9px
    classDef processClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,font-size:9px

    class Step1,Step2,Step3,Step4,Step5 stepClass
    class Auth,Check1,Check2,Check3,Check4,PayResult,Retry checkClass
    class Success,CreateTicket,Notify,Complete successClass
    class Error1,Error2,Error3,Error4,PayError,Cancel,Timeout errorClass
    class Login,Reserve,Payment,Release processClass
class NotificationSettingsModel {
  final String userId;
  final bool enablePushNotifications;
  final bool enableSystemNotifications;
  final bool enableMovieNotifications;
  final bool enablePromoNotifications;
  final bool enableTicketNotifications;
  final bool enableBugReportNotifications;
  final bool enableSound;
  final bool enableVibration;
  final String quietHoursStart; 
  final String quietHoursEnd; 
  final bool enableQuietHours;
  final List<String> mutedTypes;
  final int maxNotificationsPerDay;
  final bool groupSimilarNotifications;
  final int createdAt;
  final int updatedAt;

  NotificationSettingsModel({
    required this.userId,
    this.enablePushNotifications = true,
    this.enableSystemNotifications = true,
    this.enableMovieNotifications = true,
    this.enablePromoNotifications = true,
    this.enableTicketNotifications = true,
    this.enableBugReportNotifications = true,
    this.enableSound = true,
    this.enableVibration = true,
    this.quietHoursStart = "22:00",
    this.quietHoursEnd = "08:00",
    this.enableQuietHours = false,
    this.mutedTypes = const [],
    this.maxNotificationsPerDay = 50,
    this.groupSimilarNotifications = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationSettingsModel.fromJson(String userId, Map<String, dynamic> json) {
    return NotificationSettingsModel(
      userId: userId,
      enablePushNotifications: json['enablePushNotifications'] ?? true,
      enableSystemNotifications: json['enableSystemNotifications'] ?? true,
      enableMovieNotifications: json['enableMovieNotifications'] ?? true,
      enablePromoNotifications: json['enablePromoNotifications'] ?? true,
      enableTicketNotifications: json['enableTicketNotifications'] ?? true,
      enableBugReportNotifications: json['enableBugReportNotifications'] ?? true,
      enableSound: json['enableSound'] ?? true,
      enableVibration: json['enableVibration'] ?? true,
      quietHoursStart: json['quietHoursStart'] ?? "22:00",
      quietHoursEnd: json['quietHoursEnd'] ?? "08:00",
      enableQuietHours: json['enableQuietHours'] ?? false,
      mutedTypes: List<String>.from(json['mutedTypes'] ?? []),
      maxNotificationsPerDay: json['maxNotificationsPerDay'] ?? 50,
      groupSimilarNotifications: json['groupSimilarNotifications'] ?? true,
      createdAt: json['createdAt'] ?? 0,
      updatedAt: json['updatedAt'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enablePushNotifications': enablePushNotifications,
      'enableSystemNotifications': enableSystemNotifications,
      'enableMovieNotifications': enableMovieNotifications,
      'enablePromoNotifications': enablePromoNotifications,
      'enableTicketNotifications': enableTicketNotifications,
      'enableBugReportNotifications': enableBugReportNotifications,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'enableQuietHours': enableQuietHours,
      'mutedTypes': mutedTypes,
      'maxNotificationsPerDay': maxNotificationsPerDay,
      'groupSimilarNotifications': groupSimilarNotifications,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  
  bool isNotificationTypeEnabled(String type) {
    if (mutedTypes.contains(type)) return false;
    
    switch (type) {
      case 'system':
        return enableSystemNotifications;
      case 'movie':
        return enableMovieNotifications;
      case 'promo':
        return enablePromoNotifications;
      case 'ticket':
        return enableTicketNotifications;
      case 'bug_report':
      case 'bug_report_user':
      case 'bug_report_admin':
        return enableBugReportNotifications;
      default:
        return true;
    }
  }

  bool isInQuietHours() {
    if (!enableQuietHours) return false;
    
    final now = DateTime.now();
    final currentTime = "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}";
    
    
    if (quietHoursStart.compareTo(quietHoursEnd) > 0) {
      return currentTime.compareTo(quietHoursStart) >= 0 || 
             currentTime.compareTo(quietHoursEnd) <= 0;
    } else {
      return currentTime.compareTo(quietHoursStart) >= 0 && 
             currentTime.compareTo(quietHoursEnd) <= 0;
    }
  }

  NotificationSettingsModel copyWith({
    String? userId,
    bool? enablePushNotifications,
    bool? enableSystemNotifications,
    bool? enableMovieNotifications,
    bool? enablePromoNotifications,
    bool? enableTicketNotifications,
    bool? enableBugReportNotifications,
    bool? enableSound,
    bool? enableVibration,
    String? quietHoursStart,
    String? quietHoursEnd,
    bool? enableQuietHours,
    List<String>? mutedTypes,
    int? maxNotificationsPerDay,
    bool? groupSimilarNotifications,
    int? createdAt,
    int? updatedAt,
  }) {
    return NotificationSettingsModel(
      userId: userId ?? this.userId,
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      enableSystemNotifications: enableSystemNotifications ?? this.enableSystemNotifications,
      enableMovieNotifications: enableMovieNotifications ?? this.enableMovieNotifications,
      enablePromoNotifications: enablePromoNotifications ?? this.enablePromoNotifications,
      enableTicketNotifications: enableTicketNotifications ?? this.enableTicketNotifications,
      enableBugReportNotifications: enableBugReportNotifications ?? this.enableBugReportNotifications,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      enableQuietHours: enableQuietHours ?? this.enableQuietHours,
      mutedTypes: mutedTypes ?? this.mutedTypes,
      maxNotificationsPerDay: maxNotificationsPerDay ?? this.maxNotificationsPerDay,
      groupSimilarNotifications: groupSimilarNotifications ?? this.groupSimilarNotifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  
  factory NotificationSettingsModel.defaultSettings(String userId) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return NotificationSettingsModel(
      userId: userId,
      createdAt: now,
      updatedAt: now,
    );
  }
}

class NotificationStatsModel {
  final String date; 
  final Map<String, Map<String, int>> stats; 
  final int totalCreated;
  final int totalRead;
  final int totalDeleted;

  NotificationStatsModel({
    required this.date,
    required this.stats,
    required this.totalCreated,
    required this.totalRead,
    required this.totalDeleted,
  });

  factory NotificationStatsModel.fromJson(String date, Map<String, dynamic> json) {
    final stats = <String, Map<String, int>>{};
    int totalCreated = 0;
    int totalRead = 0;
    int totalDeleted = 0;

    for (var typeEntry in json.entries) {
      final typeStats = <String, int>{};
      final typeData = Map<String, dynamic>.from(typeEntry.value);
      
      for (var actionEntry in typeData.entries) {
        final count = actionEntry.value as int;
        typeStats[actionEntry.key] = count;
        
        switch (actionEntry.key) {
          case 'created':
            totalCreated += count;
            break;
          case 'read':
            totalRead += count;
            break;
          case 'deleted':
            totalDeleted += count;
            break;
        }
      }
      
      stats[typeEntry.key] = typeStats;
    }

    return NotificationStatsModel(
      date: date,
      stats: stats,
      totalCreated: totalCreated,
      totalRead: totalRead,
      totalDeleted: totalDeleted,
    );
  }

  Map<String, dynamic> toJson() {
    return stats;
  }

  int getTypeCount(String type, String action) {
    return stats[type]?[action] ?? 0;
  }

  double getReadRate() {
    return totalCreated > 0 ? totalRead / totalCreated : 0.0;
  }

  double getDeleteRate() {
    return totalCreated > 0 ? totalDeleted / totalCreated : 0.0;
  }
}

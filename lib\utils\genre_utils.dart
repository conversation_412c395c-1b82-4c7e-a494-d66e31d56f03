import 'package:flutter/material.dart';

class GenreUtils {
  
  static const Map<String, List<String>> genreMapping = {
    'Hành động': ['Action'],
    'Hài': ['Comedy'],
    'Tình cảm': ['Romance'],
    'Kinh dị': ['Horror', 'Thriller'],
    'Viễn tưởng': ['Sci-Fi', 'Sci‑Fi', 'Science Fiction', 'Fantasy'],
    'Gia đình': ['Family'],
    'Phiêu lưu': ['Adventure'],
    'Hoạt hình': ['Animation'],
    'Tài liệu': ['Documentary', 'Biography'],
    'Khác': [
      'Drama',
      'Crime',
      'Mystery',
      'War',
      'History',
      'Music',
      'Musical',
      'Western',
      'Superhero',
      'Biography'
    ],
  };

  
  static const List<Map<String, dynamic>> genreList = [
    {'icon': Icons.local_fire_department, 'title': 'Hành động'},
    {'icon': Icons.sentiment_satisfied_alt, 'title': '<PERSON>à<PERSON>'},
    {'icon': Icons.favorite, 'title': 'Tình cảm'},
    {'icon': Icons.psychology, 'title': 'Kinh dị'},
    {'icon': Icons.auto_awesome, 'title': 'Viễn tưởng'},
    {'icon': Icons.family_restroom, 'title': 'Gia đình'},
    {'icon': Icons.sports_esports, 'title': 'Phiêu lưu'},
    {'icon': Icons.animation, 'title': 'Hoạt hình'},
    {'icon': Icons.school, 'title': 'Tài liệu'},
    {'icon': Icons.more_horiz, 'title': 'Khác'},
  ];

  
  static List<String> get allGenres => genreMapping.keys.toList();

  
  static List<String> getEnglishTerms(String vietnameseGenre) {
    return genreMapping[vietnameseGenre] ?? [];
  }

  
  static bool matchesGenre(List<String> movieGenres, String vietnameseGenre) {
    final englishTerms = getEnglishTerms(vietnameseGenre);
    if (englishTerms.isEmpty) return false;

    return movieGenres.any((genre) {
      
      final genreList = genre.contains(',')
          ? genre.split(',').map((g) => g.trim()).toList()
          : [genre.trim()];

      return genreList.any((singleGenre) {
        return englishTerms.any((term) =>
            singleGenre.toLowerCase() == term.toLowerCase() ||
            singleGenre.toLowerCase().contains(term.toLowerCase()));
      });
    });
  }

  
  static IconData? getGenreIcon(String vietnameseGenre) {
    final genreItem = genreList.firstWhere(
      (item) => item['title'] == vietnameseGenre,
      orElse: () => {'icon': Icons.movie},
    );
    return genreItem['icon'] as IconData?;
  }
}

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;

class DebugFirebaseConfig {
  
  static void configureForDebug() {
    try {
      
      final locale = ui.PlatformDispatcher.instance.locale;
      FirebaseAuth.instance.setLanguageCode(locale.languageCode);

      if (kDebugMode) {
        print('Debug Firebase locale set to: ${locale.languageCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Debug Firebase locale configuration failed: $e');
      }
    }
  }

  
  static void disableProblematicFeatures() {
    try {
      
      if (kDebugMode) {
        print('Debug mode: Disabling problematic Firebase features');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to disable problematic features: $e');
      }
    }
  }
}

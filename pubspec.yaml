name: dop_phim
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=2.16.2 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.5
  get: ^4.6.5
  google_fonts: ^4.0.4
  gradient_textfield: ^0.0.1
  carousel_slider: ^4.2.1
  readmore: ^2.2.0
  file: ^7.0.0

  shared_preferences: ^2.2.2
  flutter_svg: ^2.0.9
  http: ^1.1.0
  intl: ^0.18.1

  firebase_core: ^2.26.0
  firebase_auth: ^4.17.7
  cloud_firestore: ^4.8.5
  google_sign_in: ^6.1.4
  image_picker: ^1.1.2
  firebase_storage: ^11.7.7
  firebase_database: ^10.5.7
  firebase_app_check: ^0.2.1+18
  cloud_functions: ^4.7.6

  file_picker: ^8.0.0+1
  excel: ^4.0.2
  csv: ^6.0.0

  media_kit: ^1.1.10+1
  media_kit_video: ^1.2.4
  media_kit_libs_video: ^1.0.4

  permission_handler: ^11.3.1
  url_launcher: ^6.3.1

  webview_flutter: ^4.4.2

  qr_flutter: ^4.1.0
  youtube_explode_dart: ^2.4.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.1
  flutter_launcher_icons: ^0.13.1

flutter:

  uses-material-design: true

  assets:
    - assets/images/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/app_icon.png"
    background_color: "#1a1a2e"
    theme_color: "#feca57"
  windows:
    generate: true
    image_path: "assets/images/app_icon.png"
    icon_size: 48

import 'dart:developer' as developer;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'storage_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final StorageService _storageService = StorageService();

  
  FirebaseFirestore get firestore => _firestore;

  
  static const String usersCollection = 'users';
  static const String userRolesCollection = 'user_roles';

  
  UserModel? _cachedUser;
  String? _cachedUserId;

  
  void _logError(String message, dynamic error) {
    if (kDebugMode) {
      developer.log('$message: $error', name: 'AuthService');
    }
  }

  
  void _clearCache() {
    _cachedUser = null;
    _cachedUserId = null;
  }

  Future<UserModel?> login(String email, String password) async {
    try {
      final UserCredential userCredential =
          await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        return null;
      }

      UserModel user;

      try {
        
        final doc = await _firestore
            .collection(usersCollection)
            .doc(userCredential.user!.uid)
            .get();

        UserRole role = UserRole.user; 

        
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(userCredential.user!.uid)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError('Error fetching user role', roleError);
        }

        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          user = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email,
            name: data['name'] ?? email.split('@')[0],
            photoUrl: data['photoUrl'] ??
                'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
            role: role,
            isActive: data['isActive'] ?? true, // Default to true if not set
          );
        } else {
          
          user = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email,
            name: email.split('@')[0],
            photoUrl: 'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
            role: role,
          );

          
          try {
            await _firestore
                .collection(usersCollection)
                .doc(userCredential.user!.uid)
                .set(user.toJson());

            
            await _firestore
                .collection(userRolesCollection)
                .doc(userCredential.user!.uid)
                .set({
              'role': role.name,
              'updatedAt': FieldValue.serverTimestamp()
            });
          } catch (firestoreError) {
            
            _logError('Error saving user to Firestore', firestoreError);
          }
        }
      } catch (firestoreError) {
        
        _logError('Error accessing Firestore', firestoreError);

        
        user = UserModel(
          id: userCredential.user!.uid,
          email: userCredential.user!.email,
          name: email.split('@')[0],
          photoUrl: 'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
          role: UserRole
              .user, 
        );
      }

      try {
        
        await _storageService.clearUserData();
        
        await _storageService.saveUser(user);
        await _storageService.setLoggedIn(true);
      } catch (storageError) {
        
        
        _logError('Error saving user to storage during login', storageError);
      }

      return user;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        throw Exception('No user found for that email.');
      } else if (e.code == 'wrong-password') {
        throw Exception('Wrong password provided for that user.');
      } else {
        throw Exception(e.message ?? 'Authentication failed');
      }
    } catch (e) {
      throw Exception('Login error: $e');
    }
  }

  Future<UserModel?> register(
      String email, String password, String name) async {
    try {
      final UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        return null;
      }

      
      const UserRole role = UserRole.user;

      final user = UserModel(
        id: userCredential.user!.uid,
        email: userCredential.user!.email,
        name: name,
        photoUrl: 'https://ui-avatars.com/api/?name=$name',
        role: role,
      );

      
      try {
        
        await _firestore
            .collection(usersCollection)
            .doc(userCredential.user!.uid)
            .set(user.toJson());

        
        await _firestore
            .collection(userRolesCollection)
            .doc(userCredential.user!.uid)
            .set({
          'role': role.name,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp()
        });
      } catch (firestoreError) {
        
        _logError('Error saving user to Firestore during registration',
            firestoreError);
      }

      try {
        
        await _storageService.clearUserData();
        
        await _storageService.saveUser(user);
        await _storageService.setLoggedIn(true);
      } catch (storageError) {
        
        
        _logError(
            'Error saving user to storage during registration', storageError);
      }

      return user;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        throw Exception('The password provided is too weak.');
      } else if (e.code == 'email-already-in-use') {
        throw Exception('The account already exists for that email.');
      } else {
        throw Exception(e.message ?? 'Registration failed');
      }
    } catch (e) {
      throw Exception('Registration error: $e');
    }
  }

  Future<void> logout() async {
    try {
      await _auth.signOut();
      await _storageService.setLoggedIn(false);
      await _storageService.clearAll();

      
      _clearCache();
    } catch (e) {
      throw Exception('Logout error: $e');
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        throw Exception('No user found for that email address.');
      } else if (e.code == 'invalid-email') {
        throw Exception('The email address is not valid.');
      } else {
        throw Exception(e.message ?? 'Failed to send password reset email');
      }
    } catch (e) {
      throw Exception('Password reset error: $e');
    }
  }

  Future<void> changePassword(
      String currentPassword, String newPassword) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('Không có người dùng đang đăng nhập');
      }

      
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);

      
      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'wrong-password') {
        throw Exception('Mật khẩu hiện tại không đúng');
      } else if (e.code == 'weak-password') {
        throw Exception('Mật khẩu mới quá yếu');
      } else if (e.code == 'requires-recent-login') {
        throw Exception('Vui lòng đăng nhập lại để thực hiện thao tác này');
      } else {
        throw Exception(e.message ?? 'Không thể đổi mật khẩu');
      }
    } catch (e) {
      throw Exception('Lỗi đổi mật khẩu: $e');
    }
  }

  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('Không có người dùng đang đăng nhập');
      }

      if (user.emailVerified) {
        throw Exception('Email đã được xác thực');
      }

      await user.sendEmailVerification();
    } on FirebaseAuthException catch (e) {
      if (e.code == 'too-many-requests') {
        throw Exception('Quá nhiều yêu cầu. Vui lòng thử lại sau');
      } else {
        throw Exception(e.message ?? 'Không thể gửi email xác thực');
      }
    } catch (e) {
      throw Exception('Lỗi gửi email xác thực: $e');
    }
  }

  Future<bool> isLoggedIn() async {
    return _auth.currentUser != null;
  }

  
  User? getCurrentFirebaseUser() {
    return _auth.currentUser;
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final User? firebaseUser = _auth.currentUser;

      if (firebaseUser == null) {
        return null;
      }

      
      if (_cachedUser != null && _cachedUserId == firebaseUser.uid) {
        return _cachedUser;
      }

      
      try {
        final localUser = await _storageService.getUser();
        if (localUser != null) {
          
          _cachedUser = localUser;
          _cachedUserId = firebaseUser.uid;
          return localUser;
        }
      } catch (e) {
        
        _logError('Error getting user from storage', e);
      }

      
      try {
        final doc = await _firestore
            .collection(usersCollection)
            .doc(firebaseUser.uid)
            .get();

        UserRole role = UserRole.user; 

        
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(firebaseUser.uid)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError('Error fetching user role', roleError);
        }

        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          final user = UserModel(
            id: firebaseUser.uid,
            email: firebaseUser.email,
            name: data['name'] ?? firebaseUser.email?.split('@')[0],
            photoUrl: data['photoUrl'] ??
                'https://ui-avatars.com/api/?name=${firebaseUser.email?.split('@')[0]}',
            role: role,
            isActive: data['isActive'] ?? true, // Default to true if not set
          );
          try {
            await _storageService.saveUser(user);
          } catch (storageError) {
            
            _logError(
                'Error saving user to storage in getCurrentUser', storageError);
          }

          
          _cachedUser = user;
          _cachedUserId = firebaseUser.uid;

          return user;
        }
      } catch (firestoreError) {
        
        _logError(
            'Error accessing Firestore in getCurrentUser', firestoreError);
      }

      
      final basicUser = UserModel(
        id: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.email?.split('@')[0] ?? 'User',
        photoUrl:
            'https://ui-avatars.com/api/?name=${firebaseUser.email?.split('@')[0] ?? 'User'}',
        role: UserRole.user, 
      );

      try {
        await _storageService.saveUser(basicUser);
      } catch (storageError) {
        _logError('Error saving basic user to storage', storageError);
      }

      
      _cachedUser = basicUser;
      _cachedUserId = firebaseUser.uid;

      return basicUser;
    } catch (e) {
      throw Exception('Get current user error: $e');
    }
  }

  
  Future<bool> isUserAdmin(String userId) async {
    try {
      final roleDoc =
          await _firestore.collection(userRolesCollection).doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;
        return role == UserRole.admin.name || role == UserRole.developer.name;
      }

      return false;
    } catch (e) {
      _logError('Error checking admin status', e);
      return false;
    }
  }

  
  Future<bool> isUserDeveloper(String userId) async {
    try {
      final roleDoc =
          await _firestore.collection(userRolesCollection).doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        return roleData['role'] == UserRole.developer.name;
      }

      return false;
    } catch (e) {
      _logError('Error checking developer status', e);
      return false;
    }
  }

  
  Future<bool> setUserAsAdmin(String userId) async {
    return await _updateUserRole(userId, UserRole.admin);
  }

  
  Future<bool> setUserAsRegular(String userId) async {
    return await _updateUserRole(userId, UserRole.user);
  }

  
  Future<bool> setUserAsDeveloper(String userId) async {
    return await _updateUserRole(userId, UserRole.developer);
  }

  
  Future<bool> _updateUserRole(String userId, UserRole role) async {
    try {
      
      await _firestore.collection(userRolesCollection).doc(userId).set(
          {'role': role.name, 'updatedAt': FieldValue.serverTimestamp()},
          SetOptions(merge: true));

      
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update({'role': role.name});

      return true;
    } catch (e) {
      _logError('Error updating user role', e);
      return false;
    }
  }

  
  Future<List<UserModel>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection(usersCollection).get();

      if (snapshot.docs.isEmpty) {
        return [];
      }

      final List<UserModel> users = [];

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          final user = UserModel.fromJson({...data, 'id': doc.id});
          users.add(user);
        } catch (e) {
          _logError('Error parsing user document', e);
        }
      }

      return users;
    } catch (e) {
      _logError('Error getting all users', e);
      return [];
    }
  }

  
  Future<bool> updateUserProfile(String userId,
      {String? name, String? photoUrl}) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (name != null) {
        updateData['name'] = name;
      }

      if (photoUrl != null) {
        updateData['photoUrl'] = photoUrl;
      }

      
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update(updateData);

      
      final doc =
          await _firestore.collection(usersCollection).doc(userId).get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;

        
        UserRole role = UserRole.user;
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(userId)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError(
              'Error fetching user role during profile update', roleError);
        }

        
        final updatedUser = UserModel(
          id: userId,
          email: data['email'],
          name: data['name'],
          photoUrl: data['photoUrl'],
          role: role,
          isActive: data['isActive'] ?? true, // Default to true if not set
        );

        
        try {
          await _storageService.saveUser(updatedUser);
        } catch (storageError) {
          _logError('Error saving updated user to storage', storageError);
        }
      }

      return true;
    } catch (e) {
      _logError('Error updating user profile', e);
      return false;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../services/paypal_service.dart';
import '../../config/paypal_config.dart';

class PayPalTestPage extends StatefulWidget {
  const PayPalTestPage({Key? key}) : super(key: key);

  @override
  State<PayPalTestPage> createState() => _PayPalTestPageState();
}

class _PayPalTestPageState extends State<PayPalTestPage> {
  final _amountController =
      TextEditingController(text: '100000'); // 100,000 VND
  final _descriptionController =
      TextEditingController(text: 'Test Movie Ticket Payment');
  bool _isProcessing = false;
  Map<String, dynamic>? _lastResult;

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _testPayPalPayment() async {
    if (_amountController.text.isEmpty) {
      Get.snackbar('Error', 'Please enter an amount');
      return;
    }

    setState(() {
      _isProcessing = true;
      _lastResult = null;
    });

    try {
      final amount = double.parse(_amountController.text);
      final usdAmount = PayPalService.convertVndToUsd(amount);

      final result = await PayPalService.processPayment(
        amount: usdAmount,
        currency: 'USD',
        description: _descriptionController.text,
        context: context,
      );

      setState(() {
        _lastResult = result;
      });

      if (result['success'] == true) {
        Get.snackbar(
          'Success',
          'PayPal payment completed successfully!',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Failed',
          result['error'] ?? 'Payment failed',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      setState(() {
        _lastResult = {'error': e.toString()};
      });

      Get.snackbar(
        'Error',
        'Payment error: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _testSimulatedPayment() async {
    if (_amountController.text.isEmpty) {
      Get.snackbar('Error', 'Please enter an amount');
      return;
    }

    setState(() {
      _isProcessing = true;
      _lastResult = null;
    });

    try {
      final amount = double.parse(_amountController.text);
      final usdAmount = PayPalService.convertVndToUsd(amount);

      final result = await PayPalService.simulatePayment(
        amount: usdAmount,
        currency: 'USD',
        description: _descriptionController.text,
      );

      setState(() {
        _lastResult = result;
      });

      Get.snackbar(
        'Simulation Complete',
        'Simulated payment result: ${result['success']}',
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() {
        _lastResult = {'error': e.toString()};
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final envInfo = PayPalService.getEnvironmentInfo();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'PayPal Test',
          style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'PayPal Configuration',
                      style: GoogleFonts.mulish(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Environment: ${envInfo['environment']}'),
                    Text('Client ID: ${envInfo['clientId']}'),
                    Text('Configured: ${envInfo['configured']}'),
                    const SizedBox(height: 8),
                    if (!envInfo['configured'])
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'PayPal is not configured. Please check your credentials.',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    if (envInfo['configured'])
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          'PayPal is configured and ready to use.',
                          style: TextStyle(color: Colors.green),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Payment',
                      style: GoogleFonts.mulish(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Amount (VND)',
                        border: OutlineInputBorder(),
                        prefixText: '₫ ',
                      ),
                    ),

                    const SizedBox(height: 16),

                    TextField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        border: OutlineInputBorder(),
                      ),
                    ),

                    const SizedBox(height: 16),

                    
                    if (_amountController.text.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'USD Amount: \$${PayPalService.convertVndToUsd(double.tryParse(_amountController.text) ?? 0).toStringAsFixed(2)}',
                          style: const TextStyle(color: Colors.blue),
                        ),
                      ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed:
                                _isProcessing ? null : _testPayPalPayment,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: _isProcessing
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Text('Test PayPal Payment'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed:
                                _isProcessing ? null : _testSimulatedPayment,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Test Simulation'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            
            if (_lastResult != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Last Payment Result',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _lastResult.toString(),
                          style: const TextStyle(fontFamily: 'monospace'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Setup Instructions',
                      style: GoogleFonts.mulish(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(PayPalConfig.setupInstructions),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Test Account (Sandbox)',
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                              'Email: <EMAIL>'),
                          const Text('Password: *B:Bb68='),
                          const SizedBox(height: 8),
                          Text(
                            'Use this account to test PayPal payments in sandbox mode.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

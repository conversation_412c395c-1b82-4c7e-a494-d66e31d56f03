import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class DebugBookingPage extends StatefulWidget {
  const DebugBookingPage({Key? key}) : super(key: key);

  @override
  State<DebugBookingPage> createState() => _DebugBookingPageState();
}

class _DebugBookingPageState extends State<DebugBookingPage> {
  final RxString _selectedDate = ''.obs;
  final RxList<String> _availableDates = <String>[].obs;

  @override
  void initState() {
    super.initState();
    _generateAvailableDates();
  }

  void _generateAvailableDates() {
    final dates = <String>[];
    final now = DateTime.now();

    for (int i = 0; i < 7; i++) {
      final date = now.add(Duration(days: i));
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      dates.add(dateString);
    }

    _availableDates.value = dates;
    _selectedDate.value = dates.first;
    
    print('Debug: Generated dates: $dates');
    print('Debug: Selected date: ${_selectedDate.value}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Booking'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Debug Info:', style: GoogleFonts.mulish(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Obx(() => Text('Available Dates Count: ${_availableDates.length}')),
                    Obx(() => Text('Selected Date: ${_selectedDate.value}')),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        print('Manual refresh triggered');
                        _generateAvailableDates();
                      },
                      child: const Text('Refresh Dates'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            
            Text('Date Selection Test:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            
            Obx(() {
              print('Debug: Rebuilding date selection UI');
              print('Debug: Available dates: $_availableDates');
              print('Debug: Selected date: ${_selectedDate.value}');
              
              return SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _availableDates.length,
                  itemBuilder: (context, index) {
                    final dateString = _availableDates[index];
                    final date = DateTime.parse(dateString);
                    final isSelected = _selectedDate.value == dateString;
                    
                    print('Debug: Building date $dateString, isSelected: $isSelected');

                    return GestureDetector(
                      onTap: () {
                        print('Debug: Date tapped: $dateString');
                        print('Debug: Before change - Selected: ${_selectedDate.value}');
                        _selectedDate.value = dateString;
                        print('Debug: After change - Selected: ${_selectedDate.value}');
                      },
                      child: Container(
                        width: 100,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue : Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected ? Colors.blue : Colors.grey,
                            width: isSelected ? 3 : 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${date.day}/${date.month}',
                              style: GoogleFonts.mulish(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : Colors.black,
                              ),
                            ),
                            Text(
                              _getDayName(date.weekday),
                              style: GoogleFonts.mulish(
                                fontSize: 12,
                                color: isSelected ? Colors.white : Colors.black54,
                              ),
                            ),
                            if (isSelected)
                              Container(
                                margin: const EdgeInsets.only(top: 4),
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'SELECTED',
                                  style: GoogleFonts.mulish(
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              );
            }),
            
            const SizedBox(height: 16),
            
            
            Text('Manual Tests:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: () {
                    if (_availableDates.isNotEmpty) {
                      _selectedDate.value = _availableDates[0];
                      print('Debug: Set to first date: ${_availableDates[0]}');
                    }
                  },
                  child: const Text('Select First'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (_availableDates.length > 1) {
                      _selectedDate.value = _availableDates[1];
                      print('Debug: Set to second date: ${_availableDates[1]}');
                    }
                  },
                  child: const Text('Select Second'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (_availableDates.length > 2) {
                      _selectedDate.value = _availableDates[2];
                      print('Debug: Set to third date: ${_availableDates[2]}');
                    }
                  },
                  child: const Text('Select Third'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            
            Obx(() => Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Current State:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text('Selected Date: ${_selectedDate.value}'),
                    Text('Available Dates: ${_availableDates.join(', ')}'),
                    Text('Total Dates: ${_availableDates.length}'),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'T2';
      case 2:
        return 'T3';
      case 3:
        return 'T4';
      case 4:
        return 'T5';
      case 5:
        return 'T6';
      case 6:
        return 'T7';
      case 7:
        return 'CN';
      default:
        return '';
    }
  }
}

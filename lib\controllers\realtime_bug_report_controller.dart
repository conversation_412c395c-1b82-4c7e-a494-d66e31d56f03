import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_bug_report_model.dart';
import '../services/realtime_database_service.dart';
import 'auth_controller.dart';

class RealtimeBugReportController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final AuthController _authController = Get.find<AuthController>();

  
  final RxList<RealtimeBugReportModel> _allBugReports =
      <RealtimeBugReportModel>[].obs;
  final RxList<RealtimeBugReportModel> _userBugReports =
      <RealtimeBugReportModel>[].obs;
  final RxList<RealtimeBugReportModel> _filteredBugReports =
      <RealtimeBugReportModel>[].obs;
  final Rx<RealtimeBugReportModel?> _selectedBugReport =
      Rx<RealtimeBugReportModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _statusFilter = 'all'.obs;

  
  StreamSubscription? _allBugReportsSubscription;
  StreamSubscription? _userBugReportsSubscription;
  StreamSubscription? _selectedBugReportSubscription;

  
  List<RealtimeBugReportModel> get allBugReports => _allBugReports;
  List<RealtimeBugReportModel> get userBugReports => _userBugReports;
  List<RealtimeBugReportModel> get filteredBugReports => _filteredBugReports;
  RealtimeBugReportModel? get selectedBugReport => _selectedBugReport.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get statusFilter => _statusFilter.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    super.onClose();
  }

  
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        fetchBugReports();
      } else {
        _allBugReports.clear();
        _userBugReports.clear();
        _selectedBugReport.value = null;
      }
    });

    
    if (_authController.user != null) {
      fetchBugReports();
    }
  }

  
  void _cancelSubscriptions() {
    _allBugReportsSubscription?.cancel();
    _userBugReportsSubscription?.cancel();
    _selectedBugReportSubscription?.cancel();
    _allBugReportsSubscription = null;
    _userBugReportsSubscription = null;
    _selectedBugReportSubscription = null;
  }

  
  Future<void> fetchBugReports() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.isAdmin) {
        
        _allBugReportsSubscription = _realtimeService
            .getAllBugReportsStream()
            .listen(_handleAllBugReports, onError: _handleError);
      } else if (_authController.user?.id != null) {
        
        _userBugReportsSubscription = _realtimeService
            .getUserBugReportsStream(_authController.user!.id!)
            .listen(_handleUserBugReports, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải báo cáo lỗi: $e';
      _isLoading.value = false;
    }
  }

  
  void _handleAllBugReports(List<RealtimeBugReportModel> bugReports) {
    print('Received ${bugReports.length} bug reports');
    _allBugReports.value = bugReports;
    _applyStatusFilter();
    _isLoading.value = false;
  }

  
  void _handleUserBugReports(List<RealtimeBugReportModel> bugReports) {
    print('Received ${bugReports.length} user bug reports');
    _userBugReports.value = bugReports;
    _isLoading.value = false;
  }

  
  void _applyStatusFilter() {
    if (_statusFilter.value == 'all') {
      _filteredBugReports.value = _allBugReports;
    } else {
      _filteredBugReports.value = _allBugReports
          .where((report) => report.status == _statusFilter.value)
          .toList();
    }
  }

  
  void setStatusFilter(String status) {
    _statusFilter.value = status;
    _applyStatusFilter();
  }

  
  void _handleError(dynamic error) {
    print('Error in bug report stream: $error');
    _errorMessage.value = 'Lỗi khi tải báo cáo lỗi: $error';
    _isLoading.value = false;
  }

  
  void fetchBugReportDetail(String bugReportId) {
    
    _selectedBugReportSubscription?.cancel();

    
    _isLoading.value = true;
    _errorMessage.value = '';
    _selectedBugReport.value = null;

    try {
      
      _selectedBugReportSubscription =
          _realtimeService.getBugReportStream(bugReportId).listen(
        (report) {
          _selectedBugReport.value = report;
          _isLoading.value = false;

          if (report == null) {
            _errorMessage.value = 'Không tìm thấy báo cáo lỗi';
          } else {
            _errorMessage.value = '';
          }
        },
        onError: (error) {
          _errorMessage.value = 'Lỗi khi tải chi tiết báo cáo: $error';
          _isLoading.value = false;
        },
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải chi tiết báo cáo: $e';
      _isLoading.value = false;
    }
  }

  
  Future<bool> createBugReport({
    required String title,
    required String description,
    Map<String, dynamic>? additionalData,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để báo cáo lỗi';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      
      final bugReportId = await _realtimeService.createBugReport(
        title: title,
        description: description,
        reportedBy: _authController.user!.id!,
        reportedByName: _authController.user!.name ?? 'Unknown User',
        reportedByEmail: _authController.user!.email,
        additionalData: additionalData,
      );

      if (bugReportId != null) {
        
        fetchBugReports();

        return true;
      } else {
        _errorMessage.value = 'Không thể tạo báo cáo lỗi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo báo cáo lỗi: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  
  Future<bool> addResponse({
    required String bugReportId,
    required String message,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để phản hồi';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      
      final report = await _realtimeService.getBugReportById(bugReportId);

      if (report == null) {
        _errorMessage.value = 'Không tìm thấy báo cáo lỗi';
        return false;
      }

      
      final isAdminOrDev =
          _authController.isAdmin || _authController.userRole == 'developer';
      if (!isAdminOrDev && !report.allowUserResponse) {
        _errorMessage.value = 'Báo cáo lỗi này không cho phép phản hồi';
        return false;
      }

      
      final success = await _realtimeService.addBugResponse(
        bugReportId: bugReportId,
        responderId: _authController.user!.id!,
        responderName: _authController.user!.name ?? 'Unknown User',
        message: message,
        isFromAdmin: _authController.isAdmin,
        isFromDeveloper: _authController.userRole == 'developer',
      );

      if (success) {
        
        

        return true;
      } else {
        _errorMessage.value = 'Không thể thêm phản hồi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi thêm phản hồi: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  
  Future<bool> updateBugStatus({
    required String bugReportId,
    required String newStatus,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để cập nhật trạng thái';
      return false;
    }

    if (!_authController.isAdmin && _authController.userRole != 'developer') {
      _errorMessage.value = 'Bạn không có quyền cập nhật trạng thái';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final isDeveloper = _authController.userRole == 'developer';

      
      final success = await _realtimeService.updateBugStatus(
        bugReportId: bugReportId,
        newStatus: newStatus,
        updatedBy: _authController.user!.id!,
        updatedByName: _authController.user!.name ?? 'Unknown User',
        isFromDeveloper: isDeveloper,
      );

      if (success) {
        
        

        return true;
      } else {
        _errorMessage.value = 'Không thể cập nhật trạng thái';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi cập nhật trạng thái: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  
  void refreshBugReports() {
    _cancelSubscriptions();
    _allBugReports.clear();
    _userBugReports.clear();
    fetchBugReports();
  }

  
  Future<bool> updateAllowUserResponse({
    required String bugReportId,
    required bool allowUserResponse,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để cập nhật trạng thái';
      return false;
    }

    if (!_authController.isAdmin && _authController.userRole != 'developer') {
      _errorMessage.value = 'Bạn không có quyền cập nhật trạng thái';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final isDeveloper = _authController.userRole == 'developer';

      
      final success = await _realtimeService.updateAllowUserResponse(
        bugReportId: bugReportId,
        allowUserResponse: allowUserResponse,
        updatedBy: _authController.user!.id!,
        updatedByName: _authController.user!.name ?? 'Unknown User',
        isFromDeveloper: isDeveloper,
      );

      if (success) {
        
        

        return true;
      } else {
        _errorMessage.value = 'Không thể cập nhật trạng thái';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi cập nhật trạng thái: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  
  void stopListeningToBugReportDetail() {
    _selectedBugReportSubscription?.cancel();
    _selectedBugReportSubscription = null;
    _selectedBugReport.value = null;
  }

  
  void clearError() {
    _errorMessage.value = '';
  }
}

import 'dart:async';
import 'package:get/get.dart';
import 'realtime_database_service.dart';
import '../controllers/realtime_notification_controller.dart';

class NotificationSchedulerService extends GetxService {
  
  final RealtimeDatabaseService _realtimeService =
      Get.find<RealtimeDatabaseService>();

  Timer? _scheduledNotificationTimer;
  Timer? _cleanupTimer;
  Timer? _dailyResetTimer;

  
  final RxInt _processedScheduledNotifications = 0.obs;
  final RxInt _cleanedUpNotifications = 0.obs;
  final RxBool _isRunning = false.obs;

  
  int get processedScheduledNotifications =>
      _processedScheduledNotifications.value;
  int get cleanedUpNotifications => _cleanedUpNotifications.value;
  bool get isRunning => _isRunning.value;

  @override
  void onInit() {
    super.onInit();
    startSchedulers();
  }

  @override
  void onClose() {
    stopSchedulers();
    super.onClose();
  }

  
  void startSchedulers() {
    _isRunning.value = true;

    
    _scheduledNotificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _processScheduledNotifications(),
    );

    
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 6),
      (_) => _cleanupExpiredNotifications(),
    );

    
    _scheduleDailyReset();

    print('NotificationSchedulerService: All schedulers started');
  }

  
  void stopSchedulers() {
    _isRunning.value = false;

    _scheduledNotificationTimer?.cancel();
    _cleanupTimer?.cancel();
    _dailyResetTimer?.cancel();

    _scheduledNotificationTimer = null;
    _cleanupTimer = null;
    _dailyResetTimer = null;

    print('NotificationSchedulerService: All schedulers stopped');
  }

  
  Future<void> _processScheduledNotifications() async {
    try {
      
      print(
          'NotificationSchedulerService: Scheduled notifications processing temporarily disabled');
      print(
          'NotificationSchedulerService: Please deploy Firebase database rules with status index');
      print(
          'NotificationSchedulerService: See QUICK_FIX_INDEX_ERROR.md for instructions');

      
      
      
      
    } catch (e) {
      print(
          'NotificationSchedulerService: Error processing scheduled notifications: $e');
    }
  }

  
  Future<void> _cleanupExpiredNotifications() async {
    try {
      
      print('NotificationSchedulerService: Cleanup temporarily disabled');
      print(
          'NotificationSchedulerService: Please deploy Firebase database rules first');

      
      
      
      
    } catch (e) {
      print(
          'NotificationSchedulerService: Error cleaning up notifications: $e');
    }
  }

  
  void _scheduleDailyReset() {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final timeUntilMidnight = tomorrow.difference(now);

    _dailyResetTimer = Timer(timeUntilMidnight, () {
      _performDailyReset();

      
      _dailyResetTimer = Timer.periodic(
        const Duration(days: 1),
        (_) => _performDailyReset(),
      );
    });
  }

  
  void _performDailyReset() {
    try {
      
      if (Get.isRegistered<RealtimeNotificationController>()) {
        final notificationController =
            Get.find<RealtimeNotificationController>();
        notificationController.resetDailyNotificationCount();
      }

      print('NotificationSchedulerService: Daily reset completed');
    } catch (e) {
      print('NotificationSchedulerService: Error during daily reset: $e');
    }
  }

  
  Future<void> manualProcessScheduledNotifications() async {
    await _processScheduledNotifications();
  }

  Future<void> manualCleanupExpiredNotifications() async {
    await _cleanupExpiredNotifications();
  }

  void manualDailyReset() {
    _performDailyReset();
  }

  
  Map<String, dynamic> getSchedulerStatus() {
    return {
      'isRunning': _isRunning.value,
      'processedScheduledNotifications': _processedScheduledNotifications.value,
      'cleanedUpNotifications': _cleanedUpNotifications.value,
      'scheduledNotificationTimerActive':
          _scheduledNotificationTimer?.isActive ?? false,
      'cleanupTimerActive': _cleanupTimer?.isActive ?? false,
      'dailyResetTimerActive': _dailyResetTimer?.isActive ?? false,
    };
  }

  
  void restartSchedulers() {
    stopSchedulers();
    Future.delayed(const Duration(seconds: 1), () {
      startSchedulers();
    });
  }
}

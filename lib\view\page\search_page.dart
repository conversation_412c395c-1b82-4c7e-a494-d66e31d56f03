import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';
import '../../utils/app_colors.dart';
import 'movie_detail_page.dart';

class SearchPage extends StatefulWidget {
  final String? initialQuery;

  const SearchPage({Key? key, this.initialQuery}) : super(key: key);

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _searchController.text = widget.initialQuery!;
      _performSearch(widget.initialQuery!);

      
      
    } else {
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _searchFocusNode.requestFocus();
        }
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    final trimmedQuery = query.trim();
    print('SearchPage: Performing search with query: "$trimmedQuery"');

    if (trimmedQuery.isNotEmpty) {
      _movieController.searchFirebaseMovies(trimmedQuery);
    } else {
      print('SearchPage: Query is empty, clearing search');
      _movieController.clearSearch();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradientVertical,
        ),
        child: SafeArea(
          child: Column(
            children: [
              
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                      ),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        style: GoogleFonts.mulish(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        decoration: InputDecoration(
                          hintText: 'search_movies'.tr,
                          hintStyle: GoogleFonts.mulish(
                            color: Colors.white54,
                            fontSize: 16,
                          ),
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Colors.white54,
                          ),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  onPressed: () {
                                    _searchController.clear();
                                    _movieController.clearSearch();
                                    setState(() {});
                                  },
                                  icon: const Icon(
                                    Icons.clear,
                                    color: Colors.white54,
                                  ),
                                )
                              : null,
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.1),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {});
                          if (value.trim().isEmpty) {
                            _movieController.clearSearch();
                          }
                        },
                        onSubmitted: _performSearch,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () => _performSearch(_searchController.text),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'search'.tr,
                        style: GoogleFonts.mulish(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              
              Expanded(
                child: Obx(() {
                  final isLoading = _movieController.isLoadingSearch.value;
                  final searchResults = _movieController.searchResults;
                  final query = _movieController.searchQuery.value;
                  final errorMessage = _movieController.errorMessage.value;

                  print(
                      'SearchPage UI: isLoading=$isLoading, query="$query", results=${searchResults.length}, error="$errorMessage"');

                  if (query.isEmpty) {
                    print('SearchPage UI: Showing empty state');
                    return _buildEmptyState();
                  }

                  if (isLoading) {
                    print('SearchPage UI: Showing loading state');
                    return _buildLoadingState();
                  }

                  if (errorMessage.isNotEmpty) {
                    print('SearchPage UI: Showing error state: $errorMessage');
                    return _buildErrorState(errorMessage);
                  }

                  if (searchResults.isEmpty) {
                    print(
                        'SearchPage UI: Showing no results state for query "$query"');
                    return _buildNoResultsState(query);
                  }

                  print(
                      'SearchPage UI: Showing ${searchResults.length} search results');
                  return _buildSearchResults(searchResults);
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80,
            color: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'search_favorite_movies'.tr,
            style: GoogleFonts.mulish(
              fontSize: 18,
              color: Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'enter_movie_name_to_search'.tr,
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return  Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Colors.amber,
            strokeWidth: 3,
          ),
          const SizedBox(height: 16),
          Text(
            'searching'.tr,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'error_occurred'.tr,
            style: GoogleFonts.mulish(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.red[300],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _performSearch(_searchController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
            child: Text('retry'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState(String query) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'no_results_found'.tr,
            style: GoogleFonts.mulish(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'no_movies_match_query'.tr.replaceAll('{query}', '"$query"'),
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.white54,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(List<Movie> movies) {
    print('SearchPage: Building search results for ${movies.length} movies');
    for (int i = 0; i < movies.length && i < 5; i++) {
      print('  Movie $i: "${movies[i].title}" (ID: ${movies[i].id})');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: movies.length,
      itemBuilder: (context, index) {
        final movie = movies[index];
        return _buildMovieCard(movie);
      },
    );
  }

  Widget _buildMovieCard(Movie movie) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.1),
      ),
      child: InkWell(
        onTap: () => Get.to(() => MovieDetailsPage(movieId: movie.id)),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  movie.fullPosterPath,
                  width: 80,
                  height: 120,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 80,
                      height: 120,
                      color: Colors.grey[800],
                      child: const Icon(
                        Icons.movie,
                        color: Colors.white54,
                        size: 40,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),

              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      movie.title,
                      style: GoogleFonts.mulish(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (movie.subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        movie.subtitle!,
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: 8),

                    
                    if (movie.genres.isNotEmpty)
                      Wrap(
                        spacing: 4,
                        children: movie.genres.take(2).map((genre) {
                          return Chip(
                            label: Text(
                              genre,
                              style: GoogleFonts.mulish(
                                fontSize: 10,
                                color: Colors.white,
                              ),
                            ),
                            backgroundColor: Colors.blue.withOpacity(0.3),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          );
                        }).toList(),
                      ),

                    const SizedBox(height: 8),

                    
                    if (movie.voteAverage != null)
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            movie.rating,
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white54,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

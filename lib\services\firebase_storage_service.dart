import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';

class FirebaseStorageService {
  static final FirebaseStorageService _instance =
      FirebaseStorageService._internal();
  factory FirebaseStorageService() => _instance;
  FirebaseStorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  
  
  Future<String> uploadUserAvatar(String userId, File imageFile) async {
    try {
      
      final String fileName =
          'avatar_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref =
          _storage.ref().child('user_avatars').child(fileName);

      
      final UploadTask uploadTask = ref.putFile(
        imageFile,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );

      
      final TaskSnapshot snapshot = await uploadTask;

      
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('Avatar uploaded successfully: $downloadUrl');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading avatar: $e');
      }
      throw Exception('Failed to upload avatar: $e');
    }
  }

  
  Future<String> uploadUserAvatarFromBytes(
      String userId, Uint8List imageBytes, String fileName) async {
    try {
      
      final String uploadFileName =
          'avatar_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final Reference ref =
          _storage.ref().child('user_avatars').child(uploadFileName);

      
      final UploadTask uploadTask = ref.putData(
        imageBytes,
        SettableMetadata(
          contentType: 'image/jpeg',
          customMetadata: {
            'userId': userId,
            'uploadedAt': DateTime.now().toIso8601String(),
            'originalFileName': fileName,
          },
        ),
      );

      
      final TaskSnapshot snapshot = await uploadTask;

      
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('Avatar uploaded successfully from bytes: $downloadUrl');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading avatar from bytes: $e');
      }
      throw Exception('Failed to upload avatar: $e');
    }
  }

  
  Future<bool> deleteUserAvatar(String downloadUrl) async {
    try {
      
      final Reference ref = _storage.refFromURL(downloadUrl);

      
      await ref.delete();

      if (kDebugMode) {
        print('Avatar deleted successfully: $downloadUrl');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting avatar: $e');
      }
      return false;
    }
  }

  
  Future<List<String>> getUserAvatars(String userId) async {
    try {
      final ListResult result =
          await _storage.ref().child('user_avatars').listAll();

      final List<String> avatarUrls = [];

      for (final Reference ref in result.items) {
        
        final FullMetadata metadata = await ref.getMetadata();
        if (metadata.customMetadata?['userId'] == userId) {
          final String downloadUrl = await ref.getDownloadURL();
          avatarUrls.add(downloadUrl);
        }
      }

      return avatarUrls;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user avatars: $e');
      }
      return [];
    }
  }

  
  Future<void> cleanupOldAvatars(String userId) async {
    try {
      final List<String> avatarUrls = await getUserAvatars(userId);

      if (avatarUrls.length > 3) {
        
        final List<Reference> refs = [];
        for (final String url in avatarUrls) {
          refs.add(_storage.refFromURL(url));
        }

        
        refs.sort((a, b) {
          
          return a.name.compareTo(b.name);
        });

        
        for (int i = 0; i < refs.length - 3; i++) {
          await refs[i].delete();
          if (kDebugMode) {
            print('Deleted old avatar: ${refs[i].name}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old avatars: $e');
      }
    }
  }
}

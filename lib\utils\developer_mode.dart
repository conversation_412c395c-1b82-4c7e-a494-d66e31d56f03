import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../controllers/auth_controller.dart';

class DeveloperMode extends GetxController {
  static const String _developerModeKey = 'developer_mode_enabled';
  static const List<String> _developerEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  final RxBool _isDeveloperMode = false.obs;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  
  bool get isDeveloperMode => _isDeveloperMode.value;

  
  RxBool get developerModeStatus => _isDeveloperMode;

  @override
  void onInit() {
    super.onInit();
    _loadDeveloperMode();

    
    final authController = Get.find<AuthController>();
    ever(authController.isLoggedInObs, (_) {
      _checkDeveloperStatus();
    });
  }

  
  Future<void> _checkDeveloperStatus() async {
    final authController = Get.find<AuthController>();

    if (!authController.isLoggedIn) {
      _isDeveloperMode.value = false;
      print('DeveloperMode: User not logged in');
      return;
    }

    final userEmail = authController.user?.email;
    final userId = authController.user?.id;

    print(
        'DeveloperMode: Checking status for user: $userId, email: $userEmail');

    if (userEmail == null) {
      _isDeveloperMode.value = false;
      print('DeveloperMode: User email is null');
      return;
    }

    
    if (_developerEmails.contains(userEmail.toLowerCase())) {
      _isDeveloperMode.value = true;
      _saveDeveloperMode(true);
      print('DeveloperMode: User is developer by email');
      return;
    }

    
    try {
      print('DeveloperMode: Checking Firestore for user_roles/$userId');

      final roleDoc =
          await _firestore.collection('user_roles').doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;

        print('DeveloperMode: User role from user_roles: $role');

        if (role == 'developer') {
          _isDeveloperMode.value = true;
          _saveDeveloperMode(true);
          print('DeveloperMode: User is developer by user_roles document');
          return;
        }
      } else {
        print('DeveloperMode: Document does not exist in user_roles');
      }
    } catch (e) {
      
      print('DeveloperMode: Error checking user_roles: $e');
    }

    
    _isDeveloperMode.value = false;
    _saveDeveloperMode(false);
    print('DeveloperMode: User is not a developer');
  }

  
  Future<void> _saveDeveloperMode(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_developerModeKey, enabled);
    } catch (e) {
      
    }
  }

  
  Future<void> _loadDeveloperMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final enabled = prefs.getBool(_developerModeKey) ?? false;

      
      if (enabled) {
        _checkDeveloperStatus();
      } else {
        _isDeveloperMode.value = false;
      }
    } catch (e) {
      _isDeveloperMode.value = false;
    }
  }

  
  bool canShowDebugFeature() {
    return _isDeveloperMode.value;
  }

  
  bool canPerformDebugAction() {
    return _isDeveloperMode.value;
  }

  
  bool isDeveloper() {
    return _isDeveloperMode.value;
  }

  
  Future<Map<String, dynamic>> checkAndShowDeveloperStatus() async {
    final authController = Get.find<AuthController>();
    final result = <String, dynamic>{};

    
    result['isLoggedIn'] = authController.isLoggedIn;
    result['isDeveloperMode'] = _isDeveloperMode.value;
    result['userId'] = authController.user?.id;
    result['userEmail'] = authController.user?.email;

    if (!authController.isLoggedIn) {
      result['status'] = 'Not logged in';
      return result;
    }

    final userEmail = authController.user?.email;
    final userId = authController.user?.id;

    if (userEmail == null) {
      result['status'] = 'User email is null';
      return result;
    }

    
    if (_developerEmails.contains(userEmail.toLowerCase())) {
      result['status'] = 'Developer by email';
      result['emailInList'] = true;
      return result;
    }

    
    try {
      final roleDoc =
          await _firestore.collection('user_roles').doc(userId).get();

      result['userRoleExists'] = roleDoc.exists;

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;

        result['userRole'] = role;

        if (role == 'developer') {
          result['status'] = 'Developer by user_roles document';
          result['userRoleData'] = roleData;

          
          _isDeveloperMode.value = true;
          _saveDeveloperMode(true);

          return result;
        }
      }
    } catch (e) {
      result['userRoleError'] = e.toString();
    }

    result['status'] = 'Not a developer';

    return result;
  }

  
  
  bool hasAdminAccess() {
    if (_isDeveloperMode.value) {
      return true; 
    }

    
    final authController = Get.find<AuthController>();
    return authController.isAdmin;
  }

  
  Future<bool> addDeveloperAccount(String userId) async {
    if (!_isDeveloperMode.value) {
      return false;
    }

    try {
      
      await _firestore.collection('user_roles').doc(userId).set({
        'role': 'developer',
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': Get.find<AuthController>().user?.id,
      }, SetOptions(merge: true));
      return true;
    } catch (e) {
      return false;
    }
  }

  
  Future<bool> removeDeveloperAccount(String userId) async {
    if (!_isDeveloperMode.value) {
      return false;
    }

    try {
      
      await _firestore.collection('user_roles').doc(userId).set({
        'role': 'user',
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': Get.find<AuthController>().user?.id,
      }, SetOptions(merge: true));
      return true;
    } catch (e) {
      return false;
    }
  }
}

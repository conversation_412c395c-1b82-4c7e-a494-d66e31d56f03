import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/review_model.dart';

class ReviewService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'reviews';

  
  Future<List<ReviewModel>> getAllReviews() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reviews: $e');
    }
  }

  
  Future<List<ReviewModel>> getReviewsByMovie(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reviews by movie: $e');
    }
  }

  
  Future<List<ReviewModel>> getReviewsByUser(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reviews by user: $e');
    }
  }

  
  Future<ReviewModel?> getReviewById(String reviewId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(reviewId).get();
      
      if (doc.exists) {
        return ReviewModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get review: $e');
    }
  }

  
  Future<ReviewModel?> getUserReviewForMovie(String userId, int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('movieId', isEqualTo: movieId)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ReviewModel.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user review for movie: $e');
    }
  }

  
  Future<List<ReviewModel>> getVerifiedReviews(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('isVerifiedPurchase', isEqualTo: true)
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get verified reviews: $e');
    }
  }

  
  Future<List<ReviewModel>> getReviewsByRating(int movieId, double minRating, double maxRating) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('rating', isGreaterThanOrEqualTo: minRating)
          .where('rating', isLessThanOrEqualTo: maxRating)
          .where('status', isEqualTo: 'active')
          .orderBy('rating', descending: true)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reviews by rating: $e');
    }
  }

  
  Future<ReviewModel> addReview(ReviewModel review) async {
    try {
      final now = DateTime.now();
      final reviewData = review.copyWith(
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _firestore
          .collection(_collection)
          .add(reviewData.toFirestore());

      return reviewData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add review: $e');
    }
  }

  
  Future<void> updateReview(ReviewModel review) async {
    try {
      final updatedReview = review.copyWith(updatedAt: DateTime.now());
      
      await _firestore
          .collection(_collection)
          .doc(review.id)
          .update(updatedReview.toFirestore());
    } catch (e) {
      throw Exception('Failed to update review: $e');
    }
  }

  
  Future<void> deleteReview(String reviewId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(reviewId)
          .update({
            'status': 'deleted',
            'updatedAt': Timestamp.now(),
          });
    } catch (e) {
      throw Exception('Failed to delete review: $e');
    }
  }

  
  Future<void> hideReview(String reviewId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(reviewId)
          .update({
            'status': 'hidden',
            'updatedAt': Timestamp.now(),
          });
    } catch (e) {
      throw Exception('Failed to hide review: $e');
    }
  }

  
  Future<void> restoreReview(String reviewId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(reviewId)
          .update({
            'status': 'active',
            'updatedAt': Timestamp.now(),
          });
    } catch (e) {
      throw Exception('Failed to restore review: $e');
    }
  }

  
  Future<void> likeReview(String reviewId, String userId) async {
    try {
      final review = await getReviewById(reviewId);
      if (review == null) throw Exception('Review not found');

      final updatedLikedBy = [...review.likedBy];
      final updatedDislikedBy = [...review.dislikedBy];

      
      updatedDislikedBy.remove(userId);

      
      if (updatedLikedBy.contains(userId)) {
        updatedLikedBy.remove(userId);
      } else {
        updatedLikedBy.add(userId);
      }

      await _firestore.collection(_collection).doc(reviewId).update({
        'likes': updatedLikedBy.length,
        'dislikes': updatedDislikedBy.length,
        'likedBy': updatedLikedBy,
        'dislikedBy': updatedDislikedBy,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to like review: $e');
    }
  }

  
  Future<void> dislikeReview(String reviewId, String userId) async {
    try {
      final review = await getReviewById(reviewId);
      if (review == null) throw Exception('Review not found');

      final updatedLikedBy = [...review.likedBy];
      final updatedDislikedBy = [...review.dislikedBy];

      
      updatedLikedBy.remove(userId);

      
      if (updatedDislikedBy.contains(userId)) {
        updatedDislikedBy.remove(userId);
      } else {
        updatedDislikedBy.add(userId);
      }

      await _firestore.collection(_collection).doc(reviewId).update({
        'likes': updatedLikedBy.length,
        'dislikes': updatedDislikedBy.length,
        'likedBy': updatedLikedBy,
        'dislikedBy': updatedDislikedBy,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to dislike review: $e');
    }
  }

  
  Future<void> reportReview(String reviewId) async {
    try {
      await _firestore.collection(_collection).doc(reviewId).update({
        'isReported': true,
        'reportCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to report review: $e');
    }
  }

  
  Future<Map<String, dynamic>> getMovieRatingStats(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('status', isEqualTo: 'active')
          .get();

      if (snapshot.docs.isEmpty) {
        return {
          'averageRating': 0.0,
          'totalReviews': 0,
          'ratingDistribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
          'verifiedReviews': 0,
        };
      }

      double totalRating = 0.0;
      int totalReviews = 0;
      int verifiedReviews = 0;
      final ratingDistribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

      for (final doc in snapshot.docs) {
        final review = ReviewModel.fromFirestore(doc);
        totalRating += review.rating;
        totalReviews++;
        
        if (review.isVerifiedPurchase) {
          verifiedReviews++;
        }

        final ratingKey = review.rating.round();
        if (ratingKey >= 1 && ratingKey <= 5) {
          ratingDistribution[ratingKey] = (ratingDistribution[ratingKey] ?? 0) + 1;
        }
      }

      return {
        'averageRating': totalRating / totalReviews,
        'totalReviews': totalReviews,
        'ratingDistribution': ratingDistribution,
        'verifiedReviews': verifiedReviews,
      };
    } catch (e) {
      throw Exception('Failed to get movie rating stats: $e');
    }
  }

  
  Stream<List<ReviewModel>> getReviewsByMovieStream(int movieId) {
    return _firestore
        .collection(_collection)
        .where('movieId', isEqualTo: movieId)
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ReviewModel.fromFirestore(doc))
            .toList());
  }

  Stream<ReviewModel?> getReviewStream(String reviewId) {
    return _firestore
        .collection(_collection)
        .doc(reviewId)
        .snapshots()
        .map((doc) => doc.exists ? ReviewModel.fromFirestore(doc) : null);
  }

  
  Future<List<ReviewModel>> getReportedReviews() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isReported', isEqualTo: true)
          .orderBy('reportCount', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reported reviews: $e');
    }
  }
}

import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  try {
    
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: "AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ", // Replace with your API key
        authDomain: "moviefinder-98.firebaseapp.com",
        projectId: "moviefinder-98",
        storageBucket: "moviefinder-98.appspot.com",
        messagingSenderId: "123456789",
        appId: "1:123456789:web:abcdefghijklmnop",
      ),
    );

    final firestore = FirebaseFirestore.instance;
    
    print('Starting to update users with isActive field...');
    
    
    final usersSnapshot = await firestore.collection('users').get();
    
    print('Found ${usersSnapshot.docs.length} users to update');
    
    int updatedCount = 0;
    int skippedCount = 0;
    
    for (final doc in usersSnapshot.docs) {
      try {
        final data = doc.data();
        
        
        if (data.containsKey('isActive')) {
          print('User ${doc.id} already has isActive field, skipping...');
          skippedCount++;
          continue;
        }
        
        
        await doc.reference.update({
          'isActive': true,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        
        print('Updated user ${doc.id} with isActive: true');
        updatedCount++;
        
        
        await Future.delayed(const Duration(milliseconds: 100));
        
      } catch (e) {
        print('Error updating user ${doc.id}: $e');
      }
    }
    
    print('\nUpdate completed!');
    print('Updated: $updatedCount users');
    print('Skipped: $skippedCount users');
    print('Total: ${usersSnapshot.docs.length} users');
    
  } catch (e) {
    print('Error: $e');
    exit(1);
  }
  
  exit(0);
}

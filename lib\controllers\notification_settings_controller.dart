import 'package:get/get.dart';
import '../models/notification_settings_model.dart';
import '../services/realtime_database_service.dart';
import '../controllers/auth_controller.dart';

class NotificationSettingsController extends GetxController {
  final RealtimeDatabaseService _realtimeService =
      Get.find<RealtimeDatabaseService>();
  final AuthController _authController = Get.find<AuthController>();

  
  final Rx<NotificationSettingsModel?> _settings =
      Rx<NotificationSettingsModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _isSaving = false.obs;

  
  NotificationSettingsModel? get settings => _settings.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  bool get isSaving => _isSaving.value;

  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }

  
  Future<void> loadSettings() async {
    if (_authController.user?.id == null) return;

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final settingsData = await _realtimeService.getUserNotificationSettings(
        _authController.user!.id!,
      );

      if (settingsData != null) {
        _settings.value = NotificationSettingsModel.fromJson(
          _authController.user!.id!,
          settingsData,
        );
      } else {
        
        _settings.value = NotificationSettingsModel.defaultSettings(
          _authController.user!.id!,
        );
        await saveSettings();
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải cài đặt thông báo: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  
  Future<bool> saveSettings() async {
    if (_settings.value == null || _authController.user?.id == null) {
      return false;
    }

    _isSaving.value = true;
    _errorMessage.value = '';

    try {
      final updatedSettings = _settings.value!.copyWith(
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      final success = await _realtimeService.updateUserNotificationSettings(
        _authController.user!.id!,
        updatedSettings.toJson(),
      );

      if (success) {
        _settings.value = updatedSettings;
        Get.snackbar(
          'Thành công',
          'Cài đặt thông báo đã được lưu',
          snackPosition: SnackPosition.BOTTOM,
        );
        return true;
      } else {
        _errorMessage.value = 'Không thể lưu cài đặt thông báo';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi lưu cài đặt: $e';
      return false;
    } finally {
      _isSaving.value = false;
    }
  }

  
  void updateSetting(String key, dynamic value) {
    if (_settings.value == null) return;

    switch (key) {
      case 'enablePushNotifications':
        _settings.value =
            _settings.value!.copyWith(enablePushNotifications: value);
        break;
      case 'enableSystemNotifications':
        _settings.value =
            _settings.value!.copyWith(enableSystemNotifications: value);
        break;
      case 'enableMovieNotifications':
        _settings.value =
            _settings.value!.copyWith(enableMovieNotifications: value);
        break;
      case 'enablePromoNotifications':
        _settings.value =
            _settings.value!.copyWith(enablePromoNotifications: value);
        break;
      case 'enableTicketNotifications':
        _settings.value =
            _settings.value!.copyWith(enableTicketNotifications: value);
        break;
      case 'enableBugReportNotifications':
        _settings.value =
            _settings.value!.copyWith(enableBugReportNotifications: value);
        break;
      case 'enableSound':
        _settings.value = _settings.value!.copyWith(enableSound: value);
        break;
      case 'enableVibration':
        _settings.value = _settings.value!.copyWith(enableVibration: value);
        break;
      case 'enableQuietHours':
        _settings.value = _settings.value!.copyWith(enableQuietHours: value);
        break;
      case 'quietHoursStart':
        _settings.value = _settings.value!.copyWith(quietHoursStart: value);
        break;
      case 'quietHoursEnd':
        _settings.value = _settings.value!.copyWith(quietHoursEnd: value);
        break;
      case 'maxNotificationsPerDay':
        _settings.value =
            _settings.value!.copyWith(maxNotificationsPerDay: value);
        break;
      case 'groupSimilarNotifications':
        _settings.value =
            _settings.value!.copyWith(groupSimilarNotifications: value);
        break;
    }
  }

  
  void toggleNotificationType(String type) {
    if (_settings.value == null) return;

    final mutedTypes = List<String>.from(_settings.value!.mutedTypes);

    if (mutedTypes.contains(type)) {
      mutedTypes.remove(type);
    } else {
      mutedTypes.add(type);
    }

    _settings.value = _settings.value!.copyWith(mutedTypes: mutedTypes);
  }

  
  bool isNotificationTypeEnabled(String type) {
    return _settings.value?.isNotificationTypeEnabled(type) ?? true;
  }

  
  bool isInQuietHours() {
    return _settings.value?.isInQuietHours() ?? false;
  }

  
  Future<void> resetToDefaults() async {
    if (_authController.user?.id == null) return;

    _settings.value = NotificationSettingsModel.defaultSettings(
      _authController.user!.id!,
    );

    await saveSettings();
  }

  
  Map<String, dynamic> exportSettings() {
    return _settings.value?.toJson() ?? {};
  }

  
  Future<bool> importSettings(Map<String, dynamic> settingsData) async {
    if (_authController.user?.id == null) return false;

    try {
      _settings.value = NotificationSettingsModel.fromJson(
        _authController.user!.id!,
        settingsData,
      );

      return await saveSettings();
    } catch (e) {
      _errorMessage.value = 'Lỗi khi import cài đặt: $e';
      return false;
    }
  }

  
  String getPreferencesSummary() {
    if (_settings.value == null) return 'Chưa có cài đặt';

    final enabled = <String>[];
    final disabled = <String>[];

    if (_settings.value!.enableSystemNotifications) {
      enabled.add('Hệ thống');
    } else {
      disabled.add('Hệ thống');
    }

    if (_settings.value!.enableMovieNotifications) {
      enabled.add('Phim');
    } else {
      disabled.add('Phim');
    }

    if (_settings.value!.enablePromoNotifications) {
      enabled.add('Khuyến mãi');
    } else {
      disabled.add('Khuyến mãi');
    }

    if (_settings.value!.enableTicketNotifications) {
      enabled.add('Vé');
    } else {
      disabled.add('Vé');
    }

    if (_settings.value!.enableBugReportNotifications) {
      enabled.add('Báo lỗi');
    } else {
      disabled.add('Báo lỗi');
    }

    String summary = '';
    if (enabled.isNotEmpty) {
      summary += 'Bật: ${enabled.join(', ')}';
    }
    if (disabled.isNotEmpty) {
      if (summary.isNotEmpty) summary += '\n';
      summary += 'Tắt: ${disabled.join(', ')}';
    }

    if (_settings.value!.enableQuietHours) {
      if (summary.isNotEmpty) summary += '\n';
      summary +=
          'Giờ yên lặng: ${_settings.value!.quietHoursStart} - ${_settings.value!.quietHoursEnd}';
    }

    return summary.isEmpty ? 'Tất cả đã tắt' : summary;
  }
}

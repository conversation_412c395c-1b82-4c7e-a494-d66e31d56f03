import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_notification_model.dart';
import '../models/notification_settings_model.dart';
import '../services/realtime_database_service.dart';
import 'auth_controller.dart';
import 'notification_settings_controller.dart';

class RealtimeNotificationController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final AuthController _authController = Get.find<AuthController>();

  
  NotificationSettingsController? _settingsController;

  
  final RxList<NotificationViewModel> _notifications =
      <NotificationViewModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _unreadCount = 0.obs;
  final RxInt _unseenCount = 0.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _selectedFilter = 'all'.obs;
  final RxBool _enableRealTimeUpdates = true.obs;
  final RxInt _dailyNotificationCount = 0.obs;
  final RxBool _isSearching = false.obs;
  final RxString _searchQuery = ''.obs;

  
  StreamSubscription? _publicNotificationsSubscription;
  StreamSubscription? _userNotificationsSubscription;
  Timer? _scheduledNotificationTimer;
  Timer? _cleanupTimer;

  
  List<NotificationViewModel> get notifications => _notifications;
  List<NotificationViewModel> get filteredNotifications {
    List<NotificationViewModel> filtered = _notifications.toList();

    
    if (_isSearching.value && _searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      filtered = filtered
          .where((n) =>
              n.notification.title.toLowerCase().contains(query) ||
              n.notification.body.toLowerCase().contains(query))
          .toList();
    }

    
    switch (_selectedFilter.value) {
      case 'unread':
        return filtered.where((n) => !n.isRead).toList();
      case 'read':
        return filtered.where((n) => n.isRead).toList();
      case 'system':
        return filtered.where((n) => n.notification.type == 'system').toList();
      case 'movie':
        return filtered.where((n) => n.notification.type == 'movie').toList();
      case 'promo':
        return filtered.where((n) => n.notification.type == 'promo').toList();
      case 'ticket':
        return filtered.where((n) => n.notification.type == 'ticket').toList();
      default:
        return filtered;
    }
  }

  bool get isLoading => _isLoading.value;
  int get unreadCount => _unreadCount.value;
  int get unseenCount => _unseenCount.value;
  String get errorMessage => _errorMessage.value;
  String get selectedFilter => _selectedFilter.value;
  RxBool get isSearching => _isSearching;
  String get searchQuery => _searchQuery.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
    initializeEnhancedFeatures();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    _scheduledNotificationTimer?.cancel();
    _cleanupTimer?.cancel();
    _realtimeService.dispose();
    super.onClose();
  }

  
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        _fetchNotifications();
      } else {
        _notifications.clear();
        _unreadCount.value = 0;
      }
    });

    
    if (_authController.user != null) {
      _fetchNotifications();
    }
  }

  
  void _cancelSubscriptions() {
    _publicNotificationsSubscription?.cancel();
    _userNotificationsSubscription?.cancel();
    _publicNotificationsSubscription = null;
    _userNotificationsSubscription = null;
  }

  
  void _fetchNotifications() {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      
      _publicNotificationsSubscription = _realtimeService
          .getPublicNotificationsStream()
          .listen(_handlePublicNotifications, onError: _handleError);

      
      if (_authController.user?.id != null) {
        _userNotificationsSubscription = _realtimeService
            .getUserNotificationsStream(_authController.user!.id!)
            .listen(_handleUserNotificationsWithState, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải thông báo: $e';
      _isLoading.value = false;
    }
  }

  
  void _handlePublicNotifications(
      List<RealtimeNotificationModel> publicNotifications) {
    print('Received ${publicNotifications.length} public notifications');

    
    final filteredPublicNotifications =
        publicNotifications.where((notification) {
      final isAdminNotification =
          notification.data?['isAdminNotification'] == 'true';
      return !isAdminNotification || (_authController.user?.isAdmin ?? false);
    }).toList();

    
    final viewModels = filteredPublicNotifications
        .map(
            (notification) => NotificationViewModel(notification: notification))
        .toList();

    
    _updateNotifications(viewModels, isPublic: true);
  }

  
  void _handleUserNotificationsWithState(
      List<NotificationViewModel> userNotifications) {
    print('🔄 Received ${userNotifications.length} user notifications');

    
    final unreadCount = userNotifications.where((n) => !n.isRead).length;
    final readCount = userNotifications.where((n) => n.isRead).length;
    print('🔄 From stream - Read: $readCount, Unread: $unreadCount');

    
    for (int i = 0; i < userNotifications.length && i < 3; i++) {
      final n = userNotifications[i];
      print(
          '🔄 Stream notification ${i + 1}: ${n.notification.id.substring(0, 8)}... - Read: ${n.isRead}, Seen: ${n.isSeen}');
    }

    _updateNotifications(userNotifications, isPublic: false);
  }

  
  void _updateNotifications(List<NotificationViewModel> newNotifications,
      {required bool isPublic}) {
    print(
        '🔄 Updating notifications - isPublic: $isPublic, count: ${newNotifications.length}');

    if (isPublic) {
      
      _notifications.removeWhere((notification) =>
          notification.notification.isPublic &&
          (notification.notification.targetUserIds == null ||
              notification.notification.targetUserIds!.isEmpty));
      print(
          '🔄 Removed old public notifications, remaining: ${_notifications.length}');

      
      _notifications.addAll(newNotifications);
    } else {
      
      print('🔄 Merging user notifications...');

      for (var newNotification in newNotifications) {
        
        final existingIndex = _notifications.indexWhere((existing) =>
            existing.notification.id == newNotification.notification.id);

        if (existingIndex != -1) {
          
          print(
              '🔄 Updating existing notification: ${newNotification.notification.id.substring(0, 8)}...');
          _notifications[existingIndex] = newNotification;
        } else {
          
          print(
              '🔄 Adding new notification: ${newNotification.notification.id.substring(0, 8)}...');
          _notifications.add(newNotification);
        }
      }
    }

    print('🔄 Final notification count: ${_notifications.length}');

    
    _notifications.sort(
        (a, b) => b.notification.createdAt.compareTo(a.notification.createdAt));

    
    _updateCounts();

    print(
        '🔄 Final counts - Unread: ${_unreadCount.value}, Unseen: ${_unseenCount.value}');

    _isLoading.value = false;
  }

  
  void _updateCounts() {
    _unreadCount.value = _notifications.where((n) => !n.isRead).length;
    _unseenCount.value = _notifications.where((n) => !n.isSeen).length;
  }

  
  void _handleError(dynamic error) {
    print('Error in notification stream: $error');
    _errorMessage.value = 'Lỗi khi tải thông báo: $error';
    _isLoading.value = false;
  }

  
  Future<bool> markAsRead(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsRead(
          _authController.user!.id!, notificationId);

      
      
      

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã đọc: $e';
      return false;
    }
  }

  
  Future<bool> markAsSeen(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsSeen(
          _authController.user!.id!, notificationId);

      
      
      

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã xem: $e';
      return false;
    }
  }

  
  Future<bool> markAllAsRead() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsRead(_authController.user!.id!);

      
      
      

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã đọc: $e';
      return false;
    }
  }

  
  Future<bool> markAllAsSeen() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsSeen(_authController.user!.id!);

      
      
      

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã xem: $e';
      return false;
    }
  }

  
  Future<bool> deleteNotification(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.deleteNotification(
          _authController.user!.id!, notificationId);

      if (success) {
        
        _notifications.removeWhere(
            (notification) => notification.notification.id == notificationId);
        _updateCounts();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa thông báo: $e';
      return false;
    }
  }

  
  void refreshNotifications() {
    _cancelSubscriptions();
    _notifications.clear();
    _fetchNotifications();
  }

  
  void setFilter(String filter) {
    _selectedFilter.value = filter;
  }

  
  Future<String?> createNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = true,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
  }) async {
    try {
      return await _realtimeService.createNotification(
        title: title,
        body: body,
        imageUrl: imageUrl,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
        expiresIn: expiresIn,
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo thông báo: $e';
      return null;
    }
  }

  
  List<NotificationViewModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.notification.type == type).toList();
  }

  
  List<NotificationViewModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  
  List<NotificationViewModel> get readNotifications {
    return _notifications.where((n) => n.isRead).toList();
  }

  
  List<NotificationViewModel> getNotificationsByPriority(String priority) {
    return _notifications
        .where((n) => n.notification.priority == priority)
        .toList();
  }

  
  bool get hasNewNotifications => _unseenCount.value > 0;

  
  void clearError() {
    _errorMessage.value = '';
  }

  

  
  NotificationSettingsController get settingsController {
    _settingsController ??= Get.find<NotificationSettingsController>();
    return _settingsController!;
  }

  
  Future<String?> createEnhancedNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = false,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
    DateTime? scheduledAt,
    List<String>? tags,
    Map<String, dynamic>? actionButtons,
  }) async {
    try {
      return await _realtimeService.createEnhancedNotification(
        title: title,
        body: body,
        imageUrl: imageUrl,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
        expiresIn: expiresIn,
        scheduledAt: scheduledAt,
        tags: tags,
        actionButtons: actionButtons,
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo thông báo nâng cao: $e';
      return null;
    }
  }

  
  void startScheduledNotificationProcessing() {
    _scheduledNotificationTimer?.cancel();
    _scheduledNotificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _realtimeService.processScheduledNotifications(),
    );
  }

  
  void startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 6),
      (_) => _realtimeService.cleanupExpiredNotifications(),
    );
  }

  
  void toggleRealTimeUpdates() {
    _enableRealTimeUpdates.value = !_enableRealTimeUpdates.value;

    if (_enableRealTimeUpdates.value) {
      _fetchNotifications();
    } else {
      _cancelSubscriptions();
    }
  }

  
  int get dailyNotificationCount => _dailyNotificationCount.value;

  
  void resetDailyNotificationCount() {
    _dailyNotificationCount.value = 0;
  }

  
  void incrementDailyNotificationCount() {
    _dailyNotificationCount.value++;
  }

  
  Future<bool> markMultipleAsRead(List<String> notificationIds) async {
    if (_authController.user?.id == null) return false;

    try {
      bool allSuccess = true;
      for (String notificationId in notificationIds) {
        final success = await _realtimeService.markNotificationAsRead(
          _authController.user!.id!,
          notificationId,
        );
        if (!success) allSuccess = false;
      }
      return allSuccess;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu nhiều thông báo: $e';
      return false;
    }
  }

  Future<bool> deleteMultipleNotifications(List<String> notificationIds) async {
    if (_authController.user?.id == null) return false;

    try {
      bool allSuccess = true;
      for (String notificationId in notificationIds) {
        final success = await _realtimeService.deleteNotification(
          _authController.user!.id!,
          notificationId,
        );
        if (!success) allSuccess = false;
      }

      if (allSuccess) {
        
        _notifications.removeWhere((notification) =>
            notificationIds.contains(notification.notification.id));
        _updateCounts();
      }

      return allSuccess;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa nhiều thông báo: $e';
      return false;
    }
  }

  
  List<NotificationViewModel> getNotificationsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _notifications.where((notification) {
      final createdAt = notification.notification.createdAtDateTime;
      return createdAt.isAfter(startDate) && createdAt.isBefore(endDate);
    }).toList();
  }

  List<NotificationViewModel> searchNotifications(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _notifications.where((notification) {
      return notification.notification.title
              .toLowerCase()
              .contains(lowercaseQuery) ||
          notification.notification.body.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  
  Map<String, int> getNotificationStatsByType() {
    final stats = <String, int>{};
    for (var notification in _notifications) {
      final type = notification.notification.type;
      stats[type] = (stats[type] ?? 0) + 1;
    }
    return stats;
  }

  Map<String, int> getNotificationStatsByPriority() {
    final stats = <String, int>{};
    for (var notification in _notifications) {
      final priority = notification.notification.priority;
      stats[priority] = (stats[priority] ?? 0) + 1;
    }
    return stats;
  }

  double getReadRate() {
    if (_notifications.isEmpty) return 0.0;
    final readCount = _notifications.where((n) => n.isRead).length;
    return readCount / _notifications.length;
  }

  
  void initializeEnhancedFeatures() {
    startScheduledNotificationProcessing();
    startCleanupTimer();
  }

  
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _isSearching.value = query.isNotEmpty;
  }

  void clearSearch() {
    _searchQuery.value = '';
    _isSearching.value = false;
  }

  void enableSearchMode() {
    _isSearching.value = true;
    _searchQuery.value = '';
  }
}

# BÁO CÁO CHUYÊN ĐỀ TỐT NGHIỆP
## ỨNG DỤNG ĐẶT VÉ XEM PHIM "ĐỚP PHIM"

---

## MỤC LỤC

**LỜI MỞ ĐẦU** .......................................................... 1

**1. GIỚI THIỆU ĐỀ TÀI** ................................................ 2
1.1. Tổng quan về đề tài ................................................ 2
1.2. Bối cảnh và động lực phát triển .................................... 2
1.2.1. <PERSON>ố<PERSON> cảnh thị trường .............................................. 2
1.2.2. Thói quen người tiêu dùng và sự cần thiết của ứng dụng ........... 3
1.2.3. Cơ hội phát triển ................................................ 4
1.3. Mục tiêu đề tài .................................................... 4
1.3.1. Mục tiêu ngắn hạn (3-12 tháng) ................................... 4
1.3.2. Mục tiêu trung hạn (1-2 năm) ..................................... 4
1.3.3. Mục tiêu dài hạn (3-5 năm) ....................................... 4
1.4. Đối tượng người dùng mục tiêu ...................................... 5
1.4.1. Phân khúc người dùng chính ....................................... 5
1.4.2. Phân khúc người dùng thứ cấp ..................................... 5
1.5. Phạm vi và giới hạn đề tài .......................................... 5
1.5.1. Chức năng được bao gồm ........................................... 5
1.5.2. Nền tảng được hỗ trợ ............................................. 6
1.5.3. Giới hạn đề tài .................................................. 6
1.6. Lợi ích mong đợi ................................................... 6
1.6.1. Lợi ích cho người dùng ........................................... 6
1.6.2. Lợi ích cho rạp chiếu ............................................ 7
1.6.3. Lợi ích cho ngành điện ảnh ....................................... 7
1.7. Yếu tố thành công quan trọng ........................................ 7
1.7.1. Yếu tố kỹ thuật .................................................. 7
1.7.2. Yếu tố kinh doanh ................................................ 7
1.7.3. Yếu tố người dùng ................................................ 8

**2. PHÂN TÍCH YÊU CẦU** ................................................. 8
2.1. Tổng quan về yêu cầu ............................................... 8
2.1.1. Phương pháp thu thập yêu cầu ..................................... 8
2.1.2. Phân loại yêu cầu ................................................ 8
2.2. Yêu cầu chức năng chi tiết ......................................... 9
2.2.1. Module Xác thực và Quản lý người dùng ........................... 9
2.2.2. Module Khám phá và Tìm kiếm Phim ................................ 15
2.2.3. Module Đặt vé và Chọn ghế ....................................... 21
2.2.4. Module Thanh toán ................................................ 23
2.2.5. Module Quản lý Vé ................................................ 23
2.2.6. Module Thông báo ................................................. 24
2.2.7. Module Admin ..................................................... 25
2.3. Yêu cầu phi chức năng .............................................. 26
2.3.1. Hiệu suất (Performance) .......................................... 26
2.3.2. Bảo mật (Security) ............................................... 27
2.3.3. Khả năng sử dụng (Usability) ..................................... 27
2.3.4. Khả năng bảo trì (Maintainability) ............................... 28
2.3.5. Khả năng mở rộng (Scalability) ................................... 28
2.4. Yêu cầu ràng buộc .................................................. 29
2.4.1. Công nghệ ........................................................ 29
2.4.2. Nền tảng ......................................................... 29
2.4.3. Tuân thủ ......................................................... 29
2.5. Use Cases chính .................................................... 29
2.5.1. Tổng quan Use Cases .............................................. 29
2.5.2. Use Case chi tiết ................................................ 31
2.6. User Stories ....................................................... 33
2.6.1. Epic: Đặt vé xem phim ............................................ 33
2.6.2. Epic: Quản lý tài khoản .......................................... 34
2.6.3. Epic: Thanh toán ................................................. 34
2.6.4. Epic: Quản trị hệ thống .......................................... 34
2.7. Acceptance Criteria tổng quát ...................................... 35
2.7.1. Tiêu chí chất lượng .............................................. 35
2.7.2. Tiêu chí kinh doanh .............................................. 35

**3. LỰA CHỌN CÔNG NGHỆ** ............................................... 35
3.1. Tổng quan về quyết định công nghệ .................................. 35
3.1.1. Tiêu chí lựa chọn công nghệ ...................................... 36
3.2. Lý do chọn Flutter Framework ....................................... 36
3.2.1. Ưu điểm vượt trội của Flutter .................................... 36
3.2.2. So sánh với các lựa chọn khác .................................... 37
3.2.3. Flutter trong bối cảnh đề tài .................................... 38
3.2.4. Phiên bản và cấu hình Flutter .................................... 39
3.3. Firebase Ecosystem ................................................. 39
3.3.1. Tổng quan Firebase ............................................... 39
3.3.2. Firebase Services được sử dụng ................................... 39
3.3.3. Firebase Configuration ........................................... 43
3.3.4. Lợi ích của Firebase Ecosystem ................................... 44
3.4. Các thư viện và dependencies chính ................................. 45
3.4.1. State Management - GetX .......................................... 45
3.4.2. UI và Design System .............................................. 46
3.4.3. Media và Content ................................................. 47
3.4.4. Network và Data .................................................. 49
3.4.5. Utilities và Tools ............................................... 49
3.4.6. Data Processing .................................................. 51
3.4.7. Payment Integration .............................................. 51
3.4.8. Development và Testing ........................................... 52
3.4.9. Dependencies Summary ............................................. 52

---

## LỜI MỞ ĐẦU

Ngành công nghiệp điện ảnh Việt Nam đang trải qua giai đoạn phát triển mạnh mẽ với doanh thu phòng vé năm 2023 đạt hơn 3.200 tỷ VNĐ và hơn 52 triệu lượt khán giả. Tuy nhiên, việc ứng dụng công nghệ thông tin trong ngành này vẫn còn nhiều hạn chế, đặc biệt là trong việc đặt vé trực tuyến.

Chuyên đề tốt nghiệp này trình bày quá trình nghiên cứu, thiết kế và triển khai ứng dụng đặt vé xem phim "Đớp Phim" - một giải pháp toàn diện sử dụng công nghệ Flutter và Firebase. Ứng dụng được phát triển nhằm giải quyết các vấn đề thực tế trong việc đặt vé xem phim tại Việt Nam, mang lại trải nghiệm tốt hơn cho người dùng và hiệu quả cao hơn cho các rạp chiếu phim.

Báo cáo này gồm 3 phần chính: Giới thiệu đề tài, Phân tích yêu cầu, và Lựa chọn công nghệ, cung cấp cái nhìn toàn diện về quá trình phát triển một ứng dụng đặt vé hiện đại và hiệu quả.

---

## 1. GIỚI THIỆU ĐỀ TÀI

### 1.1. Tổng quan về đề tài

Đề tài "Ứng dụng đặt vé xem phim 'Đớp Phim'" là một dự án phát triển ứng dụng mobile và web toàn diện cho việc đặt vé xem phim trực tuyến. Ứng dụng được xây dựng trên nền tảng Flutter framework kết hợp với Firebase ecosystem, cho phép người dùng dễ dàng tìm kiếm phim, chọn rạp, đặt vé và thanh toán một cách thuận tiện và an toàn.

Tên ứng dụng "Đớp Phim" thể hiện sự nhanh chóng, tiện lợi trong việc "đớp" lấy những suất chiếu yêu thích, phản ánh đúng mục tiêu của ứng dụng là mang đến trải nghiệm đặt vé nhanh chóng và hiệu quả cho người dùng Việt Nam.

### 1.2. Bối cảnh và động lực phát triển

#### 1.2.1. Bối cảnh thị trường

Ngành công nghiệp điện ảnh Việt Nam đang trải qua giai đoạn phát triển mạnh mẽ và bền vững:

**Thống kê thị trường 2023:**
- Doanh thu phòng vé: Hơn 3.200 tỷ VNĐ (tăng 15% so với 2022)
- Lượt khán giả: Hơn 52 triệu lượt người xem phim tại rạp
- Hạ tầng rạp chiếu: 1.200 rạp với hơn 180.000 ghế trên toàn quốc
- Phân bố địa lý: Tập trung chủ yếu tại TP.HCM, Hà Nội, Đà Nẵng

**Thách thức hiện tại:**
- Phần lớn rạp chiếu vẫn sử dụng phương thức bán vé truyền thống
- Thiếu nền tảng tổng hợp cho phép đặt vé từ nhiều rạp khác nhau
- Trải nghiệm người dùng chưa tối ưu với quy trình đặt vé phức tạp
- Hạn chế trong tích hợp công nghệ và dịch vụ thanh toán

#### 1.2.2. Thói quen người tiêu dùng và sự cần thiết của ứng dụng

Theo khảo sát Nielsen Vietnam (2023):

**Xu hướng số hóa:**
- 78% khán giả sử dụng smartphone để tìm hiểu thông tin phim
- 65% mong muốn đặt vé trực tuyến thay vì đến quầy vé
- 82% quan tâm đến việc chọn ghế trước khi đến rạp
- 71% sẵn sàng thanh toán điện tử cho vé xem phim

**Điểm đau của người dùng:**
- Thời gian chờ đợi: Xếp hàng tại quầy vé, đặc biệt cuối tuần
- Thông tin hạn chế: Khó so sánh lịch chiếu, giá vé giữa các rạp
- Trải nghiệm không tối ưu: Không biết trước tình trạng ghế trống
- Thanh toán bất tiện: Chỉ chấp nhận tiền mặt tại nhiều rạp

#### 1.2.3. Cơ hội phát triển

**Thị trường tiềm năng:**
- Tỷ lệ smartphone penetration: 84% dân số Việt Nam (2023)
- Tăng trưởng thương mại điện tử: 25% năm/năm
- Thói quen thanh toán số: 68% người dùng sử dụng ví điện tử

**Lợi thế cạnh tranh:**
- Ứng dụng đa rạp đầu tiên tại Việt Nam
- Tích hợp sâu với hệ sinh thái thanh toán Việt Nam
- UI/UX được thiết kế riêng cho người dùng Việt
- Chi phí phát triển thấp nhờ cross-platform

### 1.3. Mục tiêu đề tài

#### 1.3.1. Mục tiêu ngắn hạn (3-12 tháng)

**Phát triển MVP:**
- Hoàn thành ứng dụng với đầy đủ tính năng cơ bản
- Tích hợp 5-10 rạp chiếu phim tại TP.HCM
- Đạt 1.000 người dùng đăng ký trong 3 tháng đầu
- Xử lý 500 giao dịch đặt vé/tháng

#### 1.3.2. Mục tiêu trung hạn (1-2 năm)

**Mở rộng thị trường:**
- Tích hợp 50+ rạp chiếu trên toàn quốc
- Đạt 10.000 người dùng hoạt động hàng tháng
- Xử lý 5.000 giao dịch/tháng
- Mở rộng ra Hà Nội và Đà Nẵng

#### 1.3.3. Mục tiêu dài hạn (3-5 năm)

**Trở thành nền tảng hàng đầu:**
- Market leader trong đặt vé trực tuyến tại Việt Nam
- 100.000+ người dùng hoạt động
- Tích hợp 200+ rạp chiếu toàn quốc
- Mở rộng sang Đông Nam Á

### 1.4. Đối tượng người dùng mục tiêu

#### 1.4.1. Phân khúc người dùng chính

**Người dùng cá nhân (Primary Users)**
- Độ tuổi: 18-35 tuổi
- Thu nhập: Trung bình trở lên (>8 triệu VNĐ/tháng)
- Địa lý: Thành phố lớn (TP.HCM, Hà Nội, Đà Nẵng)
- Hành vi: Xem phim 2-4 lần/tháng, tech-savvy

**Gia đình có con (Family Users)**
- Độ tuổi: 25-45 tuổi
- Đặc điểm: Có con từ 6-16 tuổi, quan tâm đến phim gia đình
- Nhu cầu: Đặt vé nhóm, chọn ghế gần nhau

**Nhóm bạn trẻ (Young Groups)**
- Độ tuổi: 16-25 tuổi
- Đặc điểm: Học sinh, sinh viên, nhân viên trẻ
- Nhu cầu: Đặt vé nhóm, chia sẻ chi phí, phim trending

#### 1.4.2. Phân khúc người dùng thứ cấp

**Quản trị viên rạp chiếu (Cinema Admins)**
- Vai trò: Quản lý lịch chiếu, giá vé, báo cáo doanh thu
- Nhu cầu: Dashboard analytics, quản lý inventory

**Nhân viên rạp chiếu (Cinema Staff)**
- Vai trò: Xử lý đặt vé, check-in khách hàng
- Nhu cầu: Mobile app cho staff, QR code scanning

### 1.5. Phạm vi và giới hạn đề tài

#### 1.5.1. Chức năng được bao gồm

**Core Features:**
- Đăng ký/đăng nhập (email, Google Sign-In)
- Tìm kiếm và lọc phim theo thể loại, rạp, thời gian
- Xem chi tiết phim (synopsis, cast, trailer, rating)
- Đặt vé với quy trình 5 bước
- Chọn ghế real-time với conflict resolution
- Thanh toán PayPal an toàn
- Quản lý vé điện tử với QR code
- Thông báo push và in-app
- Admin panel cho quản lý rạp/lịch chiếu

#### 1.5.2. Nền tảng được hỗ trợ

**Mobile Platforms:**
- Android 5.0+ (API Level 21+)
- iOS 11.0+
- Responsive design cho tablet

**Web Platform:**
- Progressive Web App (PWA)
- Modern browsers: Chrome 90+, Safari 14+, Firefox 88+

#### 1.5.3. Giới hạn đề tài

**Chức năng không bao gồm:**
- Hệ thống POS tại rạp chiếu
- Quản lý F&B (đồ ăn, nước uống)
- Tích hợp hệ thống kế toán/ERP
- Live streaming phim

**Giới hạn kỹ thuật:**
- Chỉ hỗ trợ PayPal cho thanh toán (giai đoạn đầu)
- Không hỗ trợ offline booking
- Giới hạn 8 ghế/giao dịch

### 1.6. Lợi ích mong đợi

#### 1.6.1. Lợi ích cho người dùng

**Tiện lợi và tiết kiệm thời gian:**
- Đặt vé 24/7 từ bất kỳ đâu
- Không cần xếp hàng tại quầy vé
- So sánh giá và lịch chiếu nhiều rạp

**Trải nghiệm tốt hơn:**
- Chọn ghế trước khi đến rạp
- Thông tin phim phong phú với trailer
- Giao diện thân thiện, dễ sử dụng

#### 1.6.2. Lợi ích cho rạp chiếu

**Tăng doanh thu:**
- Tiếp cận khách hàng rộng hơn
- Bán vé online 24/7
- Giảm chi phí nhân sự quầy vé

**Quản lý hiệu quả:**
- Dashboard analytics real-time
- Dự đoán nhu cầu chính xác
- Báo cáo doanh thu chi tiết

#### 1.6.3. Lợi ích cho ngành điện ảnh

**Chuyển đổi số:**
- Thúc đẩy digitalization trong ngành
- Nâng cao trải nghiệm khách hàng
- Tăng hiệu quả vận hành

### 1.7. Yếu tố thành công quan trọng

#### 1.7.1. Yếu tố kỹ thuật

**Performance và Reliability:**
- Thời gian phản hồi < 1 giây
- Uptime 99.5%+
- Scalability cho 10,000+ concurrent users

**Security và Compliance:**
- PCI DSS compliance cho payment
- End-to-end encryption
- Secure authentication và authorization

#### 1.7.2. Yếu tố kinh doanh

**Partnership Strategy:**
- Onboard key cinema chains
- Competitive commission structure
- Value-added services

**Revenue Model:**
- Commission từ ticket sales (5-8%)
- Premium features cho cinemas
- Advertising revenue

#### 1.7.3. Yếu tố người dùng

**User Acquisition:**
- App Store Optimization (ASO)
- Social media marketing
- Partnership với cinemas

**User Retention:**
- Loyalty program hiệu quả
- Personalized recommendations
- Excellent customer support

---

*[Nội dung tiếp tục với các phần 2 và 3 theo mục lục đã cung cấp...]*

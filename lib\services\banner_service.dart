import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/banner_model.dart';

class BannerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'banners';

  
  void _log(String message) {
    developer.log(message, name: 'BannerService');
  }

  Future<List<BannerModel>> getBanners({BannerType? type}) async {
    try {
      _log('Getting all banners, type: ${type?.toString() ?? 'all'}');

      Query query = _firestore.collection(_collection);

      if (type != null) {
        final typeStr = type.toString().split('.').last;
        _log('Filtering by type: $typeStr');
        query = query.where('type', isEqualTo: typeStr);
      }

      _log('Executing query');
      final snapshot = await query.get();
      _log('Query returned ${snapshot.docs.length} documents');

      if (snapshot.docs.isEmpty) {
        _log('No banners found');
        return [];
      }

      final List<BannerModel> banners = [];

      for (final doc in snapshot.docs) {
        try {
          final banner = BannerModel.fromFirestore(doc);
          banners.add(banner);
        } catch (e) {
          _log('Error parsing document ${doc.id}: $e');
        }
      }
      banners.sort((a, b) {
        final orderComparison = (a.order ?? 0).compareTo(b.order ?? 0);
        if (orderComparison != 0) return orderComparison;
        return b.createdAt.compareTo(a.createdAt);
      });

      _log('Returning ${banners.length} sorted banner models');
      return banners;
    } catch (e) {
      _log('Error getting banners: $e');
      throw Exception('Failed to get banners: $e');
    }
  }

  Future<List<BannerModel>> getActiveBanners({BannerType? type}) async {
    try {
      _log('Getting active banners, type: ${type?.toString() ?? 'all'}');
      final allDocsSnapshot =
          await _firestore.collection(_collection).limit(1).get();
      _log(
          'Collection check: ${allDocsSnapshot.docs.isEmpty ? "Collection is empty" : "Collection has documents"}');

      if (allDocsSnapshot.docs.isEmpty) {
        _log('Collection appears empty, returning empty list');
        return [];
      }

      Query query = _firestore.collection(_collection);

      if (type != null) {
        final typeStr = type.toString().split('.').last;
        _log('Filtering by type: $typeStr');
        query = query.where('type', isEqualTo: typeStr);
      }

      _log('Executing query without filters to check all documents');
      final allSnapshot = await query.get();
      _log(
          'Query without filters returned ${allSnapshot.docs.length} documents');

      query = query.where('isActive', isEqualTo: true);

      _log('Executing final query with filters');
      final snapshot = await query.get();
      _log('Final query returned ${snapshot.docs.length} documents');

      if (snapshot.docs.isEmpty) {
        _log('No active banners found');
        return [];
      }

      final List<BannerModel> banners = [];

      for (final doc in snapshot.docs) {
        try {
          _log('Parsing document: ${doc.id}');
          final data = doc.data() as Map<String, dynamic>;
          _log('Document data: $data');

          final banner = BannerModel.fromFirestore(doc);
          banners.add(banner);
        } catch (e) {
          _log('Error parsing document ${doc.id}: $e');
          
        }
      }

      
      banners.sort((a, b) {
        
        final orderComparison = (a.order ?? 0).compareTo(b.order ?? 0);
        if (orderComparison != 0) return orderComparison;

        
        return b.createdAt.compareTo(a.createdAt);
      });

      _log('Returning ${banners.length} sorted banner models');
      return banners;
    } catch (e) {
      _log('Error getting active banners: $e');
      throw Exception('Failed to get active banners: $e');
    }
  }

  
  Future<BannerModel?> getBannerById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();

      if (!doc.exists) {
        return null;
      }

      return BannerModel.fromFirestore(doc);
    } catch (e) {
      throw Exception('Failed to get banner: $e');
    }
  }

  Future<BannerModel> addBanner(BannerModel banner) async {
    try {
      int? order = banner.order;
      order ??= await _getNextOrderNumber(banner.type);

      final now = DateTime.now();
      final data = banner
          .copyWith(
            createdAt: now,
            updatedAt: now,
            order: order,
          )
          .toJson();

      final docRef = await _firestore.collection(_collection).add(data);

      return banner.copyWith(
        id: docRef.id,
        order: order,
        createdAt: now,
        updatedAt: now,
      );
    } catch (e) {
      throw Exception('Failed to add banner: $e');
    }
  }

  Future<void> updateBanner(BannerModel banner) async {
    try {
      final data = banner
          .copyWith(
            updatedAt: DateTime.now(),
          )
          .toJson();

      await _firestore.collection(_collection).doc(banner.id).update(data);
    } catch (e) {
      throw Exception('Failed to update banner: $e');
    }
  }

  Future<void> deleteBanner(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
    } catch (e) {
      throw Exception('Failed to delete banner: $e');
    }
  }

  Future<void> toggleBannerStatus(String id, bool isActive) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to toggle banner status: $e');
    }
  }

  Future<void> reorderBanners(List<BannerModel> banners) async {
    try {
      final batch = _firestore.batch();

      for (int i = 0; i < banners.length; i++) {
        final banner = banners[i];
        final docRef = _firestore.collection(_collection).doc(banner.id);

        batch.update(docRef, {
          'order': i,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to reorder banners: $e');
    }
  }

  Future<int> _getNextOrderNumber(BannerType type) async {
    try {
      _log('Getting next order number for type: ${type.toString()}');

      final snapshot = await _firestore
          .collection(_collection)
          .where('type', isEqualTo: type.toString().split('.').last)
          .get();

      if (snapshot.docs.isEmpty) {
        _log('No existing banners of this type, returning order 0');
        return 0;
      }

      int maxOrder = 0;
      for (final doc in snapshot.docs) {
        try {
          final banner = BannerModel.fromFirestore(doc);
          final order = banner.order ?? 0;
          if (order > maxOrder) {
            maxOrder = order;
          }
        } catch (e) {
          _log('Error parsing document ${doc.id}: $e');
        }
      }

      _log('Highest existing order is $maxOrder, returning ${maxOrder + 1}');
      return maxOrder + 1;
    } catch (e) {
      _log('Error getting next order number: $e, returning 0');

      return 0;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';

class MovieEditPage extends StatefulWidget {
  final Movie? movie;

  const MovieEditPage({Key? key, this.movie}) : super(key: key);

  @override
  State<MovieEditPage> createState() => _MovieEditPageState();
}

class _MovieEditPageState extends State<MovieEditPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final _formKey = GlobalKey<FormState>();

  late TextEditingController _titleController;
  late TextEditingController _originalTitleController;
  late TextEditingController _overviewController;
  late TextEditingController _posterPathController;
  late TextEditingController _backdropPathController;
  late TextEditingController _trailerUrlController;
  late TextEditingController _genresController;

  
  final List<String> _predefinedGenres = [
    'Hành động',
    'Hài',
    'T<PERSON><PERSON> cảm',
    'Kinh dị',
    'Viễn tưởng',
    'Gia đình',
    'Phiêu lưu',
    'Hoạt hình',
    'Tài liệu',
    'Khác',
  ];

  List<String> _selectedGenres = [];
  String _customGenre = '';
  late TextEditingController _releaseDateController;
  late TextEditingController _runtimeController;
  late TextEditingController _voteAverageController;
  late TextEditingController _ageRatingController;
  late TextEditingController _directorController;

  MovieStatus _selectedStatus = MovieStatus.nowPlaying;
  bool _isActive = true;
  bool _isHomeBanner = false;
  bool _isSplashBanner = false;
  int? _bannerOrder;

  String _errorMessage = '';
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.movie != null;
    _initializeControllers();
  }

  void _initializeControllers() {
    final movie = widget.movie;

    _titleController = TextEditingController(text: movie?.title ?? '');
    _originalTitleController =
        TextEditingController(text: movie?.originalTitle ?? '');
    _overviewController = TextEditingController(text: movie?.overview ?? '');
    _posterPathController =
        TextEditingController(text: movie?.posterPath ?? '');
    _backdropPathController =
        TextEditingController(text: movie?.backdropPath ?? '');
    _trailerUrlController =
        TextEditingController(text: movie?.trailerUrl ?? '');
    _genresController =
        TextEditingController(text: movie?.genres.join(', ') ?? '');
    _releaseDateController =
        TextEditingController(text: movie?.releaseDate ?? '');
    _runtimeController =
        TextEditingController(text: movie?.runtime?.toString() ?? '');
    _voteAverageController =
        TextEditingController(text: movie?.voteAverage?.toString() ?? '');
    _ageRatingController = TextEditingController(text: movie?.ageRating ?? '');
    _directorController = TextEditingController(text: movie?.director ?? '');

    
    if (movie != null) {
      _selectedStatus = movie.status;
      _isActive = movie.isActive;
      _isHomeBanner = movie.isHomeBanner;
      _isSplashBanner = movie.isSplashBanner;
      _bannerOrder = movie.bannerOrder;

      
      _selectedGenres = movie.genres
          .where((genre) => _predefinedGenres.contains(genre))
          .toList();
      final customGenres = movie.genres
          .where((genre) => !_predefinedGenres.contains(genre))
          .toList();
      if (customGenres.isNotEmpty) {
        _selectedGenres.add('Khác');
        _customGenre = customGenres.join(', ');
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _originalTitleController.dispose();
    _overviewController.dispose();
    _posterPathController.dispose();
    _backdropPathController.dispose();
    _trailerUrlController.dispose();
    _genresController.dispose();
    _releaseDateController.dispose();
    _runtimeController.dispose();
    _voteAverageController.dispose();
    _ageRatingController.dispose();
    _directorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff2B5876),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        _isEditing ? 'Chỉnh sửa phim' : 'Thêm phim mới',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Obx(() => _movieController.isSubmitting.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : IconButton(
                            onPressed: _saveMovie,
                            icon: const Icon(Icons.save, color: Colors.white),
                          )),
                  ],
                ),
              ),

              
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_errorMessage.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Colors.red.withOpacity(0.3)),
                            ),
                            child: Text(
                              _errorMessage,
                              style: GoogleFonts.mulish(color: Colors.red[300]),
                            ),
                          ),

                        _buildTextField(
                          controller: _titleController,
                          label: 'Tên phim *',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Vui lòng nhập tên phim';
                            }
                            return null;
                          },
                        ),

                        _buildTextField(
                          controller: _originalTitleController,
                          label: 'Tên gốc',
                        ),

                        _buildTextField(
                          controller: _overviewController,
                          label: 'Mô tả',
                          maxLines: 3,
                        ),

                        _buildTextField(
                          controller: _posterPathController,
                          label: 'Poster Path',
                          hint: '/path/to/poster.jpg',
                        ),

                        _buildTextField(
                          controller: _backdropPathController,
                          label: 'Backdrop Path',
                          hint: '/path/to/backdrop.jpg',
                        ),

                        _buildTextField(
                          controller: _trailerUrlController,
                          label: 'Trailer URL',
                          hint: 'https://youtube.com/watch?v=...',
                        ),

                        _buildGenreSelector(),

                        _buildTextField(
                          controller: _releaseDateController,
                          label: 'Ngày phát hành',
                          hint: 'YYYY-MM-DD',
                        ),

                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _runtimeController,
                                label: 'Thời lượng (phút)',
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildTextField(
                                controller: _voteAverageController,
                                label: 'Điểm đánh giá',
                                keyboardType: TextInputType.number,
                                hint: '0.0 - 10.0',
                              ),
                            ),
                          ],
                        ),

                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _ageRatingController,
                                label: 'Độ tuổi',
                                hint: 'PG, PG-13, R, 18+',
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildTextField(
                                controller: _directorController,
                                label: 'Đạo diễn',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 24),

                        
                        Text(
                          'Trạng thái phim',
                          style: GoogleFonts.mulish(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.white30),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<MovieStatus>(
                              value: _selectedStatus,
                              isExpanded: true,
                              dropdownColor: const Color(0xff4E4376),
                              style: GoogleFonts.mulish(color: Colors.white),
                              items: MovieStatus.values.map((status) {
                                return DropdownMenuItem(
                                  value: status,
                                  child: Text(status.displayName),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedStatus = value;
                                  });
                                }
                              },
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        
                        Text(
                          'Cài đặt Banner',
                          style: GoogleFonts.mulish(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),

                        CheckboxListTile(
                          title: Text(
                            'Hiển thị trong Home Banner',
                            style: GoogleFonts.mulish(color: Colors.white),
                          ),
                          value: _isHomeBanner,
                          onChanged: (value) {
                            setState(() {
                              _isHomeBanner = value ?? false;
                            });
                          },
                          activeColor: Colors.amber,
                          checkColor: Colors.black,
                        ),

                        CheckboxListTile(
                          title: Text(
                            'Hiển thị trong Splash Banner',
                            style: GoogleFonts.mulish(color: Colors.white),
                          ),
                          value: _isSplashBanner,
                          onChanged: (value) {
                            setState(() {
                              _isSplashBanner = value ?? false;
                            });
                          },
                          activeColor: Colors.amber,
                          checkColor: Colors.black,
                        ),

                        if (_isHomeBanner || _isSplashBanner)
                          _buildTextField(
                            controller: TextEditingController(
                                text: _bannerOrder?.toString() ?? ''),
                            label: 'Thứ tự Banner',
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              _bannerOrder = int.tryParse(value);
                            },
                          ),

                        CheckboxListTile(
                          title: Text(
                            'Kích hoạt',
                            style: GoogleFonts.mulish(color: Colors.white),
                          ),
                          value: _isActive,
                          onChanged: (value) {
                            setState(() {
                              _isActive = value ?? true;
                            });
                          },
                          activeColor: Colors.amber,
                          checkColor: Colors.black,
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenreSelector() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thể loại',
            style: GoogleFonts.mulish(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),

          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _predefinedGenres.map((genre) {
              final isSelected = _selectedGenres.contains(genre);
              return FilterChip(
                label: Text(genre),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedGenres.add(genre);
                    } else {
                      _selectedGenres.remove(genre);
                      if (genre == 'Khác') {
                        _customGenre = '';
                      }
                    }
                  });
                },
                backgroundColor: Colors.white.withOpacity(0.1),
                selectedColor: Colors.amber.withOpacity(0.3),
                labelStyle: GoogleFonts.mulish(
                  color: isSelected ? Colors.white : Colors.white70,
                ),
                side: BorderSide(
                  color: isSelected ? Colors.amber : Colors.white30,
                ),
              );
            }).toList(),
          ),

          
          if (_selectedGenres.contains('Khác'))
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: TextFormField(
                initialValue: _customGenre,
                onChanged: (value) {
                  _customGenre = value;
                },
                style: GoogleFonts.mulish(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Nhập thể loại khác...',
                  hintStyle: GoogleFonts.mulish(color: Colors.white54),
                  filled: true,
                  fillColor: Colors.white.withOpacity(0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.white30),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.amber),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.mulish(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: controller,
            maxLines: maxLines,
            keyboardType: keyboardType,
            validator: validator,
            onChanged: onChanged,
            autofocus: false, 
            style: GoogleFonts.mulish(color: Colors.white),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: GoogleFonts.mulish(color: Colors.white54),
              filled: true,
              fillColor: Colors.white.withOpacity(0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.white30),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.white30),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.amber),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveMovie() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _errorMessage = '';
    });

    try {
      final now = DateTime.now();
      
      final genres = <String>[];
      for (final genre in _selectedGenres) {
        if (genre == 'Khác') {
          if (_customGenre.isNotEmpty) {
            genres.addAll(_customGenre
                .split(',')
                .map((g) => g.trim())
                .where((g) => g.isNotEmpty));
          }
        } else {
          genres.add(genre);
        }
      }

      if (_isEditing) {
        
        final updatedMovie = widget.movie!.copyWith(
          title: _titleController.text.trim(),
          originalTitle: _originalTitleController.text.trim().isEmpty
              ? null
              : _originalTitleController.text.trim(),
          overview: _overviewController.text.trim().isEmpty
              ? null
              : _overviewController.text.trim(),
          posterPath: _posterPathController.text.trim().isEmpty
              ? null
              : _posterPathController.text.trim(),
          backdropPath: _backdropPathController.text.trim().isEmpty
              ? null
              : _backdropPathController.text.trim(),
          trailerUrl: _trailerUrlController.text.trim().isEmpty
              ? null
              : _trailerUrlController.text.trim(),
          genres: genres,
          releaseDate: _releaseDateController.text.trim().isEmpty
              ? null
              : _releaseDateController.text.trim(),
          runtime: int.tryParse(_runtimeController.text.trim()),
          voteAverage: double.tryParse(_voteAverageController.text.trim()),
          ageRating: _ageRatingController.text.trim().isEmpty
              ? null
              : _ageRatingController.text.trim(),
          director: _directorController.text.trim().isEmpty
              ? null
              : _directorController.text.trim(),
          status: _selectedStatus,
          isActive: _isActive,
          isHomeBanner: _isHomeBanner,
          isSplashBanner: _isSplashBanner,
          bannerOrder: _bannerOrder,
          updatedAt: now,
        );

        final success = await _movieController.updateMovie(updatedMovie);

        if (success) {
          Get.back(result: true);
          Get.snackbar(
            'Thành công',
            'Phim đã được cập nhật',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          setState(() {
            _errorMessage = _movieController.errorMessage.value;
          });
        }
      } else {
        
        final newMovie = Movie(
          id: DateTime.now().millisecondsSinceEpoch, 
          title: _titleController.text.trim(),
          originalTitle: _originalTitleController.text.trim().isEmpty
              ? null
              : _originalTitleController.text.trim(),
          overview: _overviewController.text.trim().isEmpty
              ? null
              : _overviewController.text.trim(),
          posterPath: _posterPathController.text.trim().isEmpty
              ? null
              : _posterPathController.text.trim(),
          backdropPath: _backdropPathController.text.trim().isEmpty
              ? null
              : _backdropPathController.text.trim(),
          trailerUrl: _trailerUrlController.text.trim().isEmpty
              ? null
              : _trailerUrlController.text.trim(),
          genres: genres,
          releaseDate: _releaseDateController.text.trim().isEmpty
              ? null
              : _releaseDateController.text.trim(),
          runtime: int.tryParse(_runtimeController.text.trim()),
          voteAverage: double.tryParse(_voteAverageController.text.trim()),
          ageRating: _ageRatingController.text.trim().isEmpty
              ? null
              : _ageRatingController.text.trim(),
          director: _directorController.text.trim().isEmpty
              ? null
              : _directorController.text.trim(),
          status: _selectedStatus,
          isActive: _isActive,
          isHomeBanner: _isHomeBanner,
          isSplashBanner: _isSplashBanner,
          bannerOrder: _bannerOrder,
          createdAt: now,
          updatedAt: now,
        );

        final success = await _movieController.addMovie(newMovie);

        if (success) {
          Get.back(result: true);
          Get.snackbar(
            'Thành công',
            'Phim đã được thêm',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          setState(() {
            _errorMessage = _movieController.errorMessage.value;
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi: ${e.toString()}';
      });
    }
  }
}

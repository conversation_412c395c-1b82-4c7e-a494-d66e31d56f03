

echo "🔥 Firebase Realtime Database Rules Deployment"
echo "=============================================="

if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed"
    echo "📦 Installing Firebase CLI..."
    npm install -g firebase-tools
else
    echo "✅ Firebase CLI is already installed"
fi

echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase"
    echo "🔑 Please login to Firebase..."
    firebase login
else
    echo "✅ Already logged in to Firebase"
fi

echo "📋 Available Firebase projects:"
firebase projects:list

if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found"
    echo "🔧 Initializing Firebase project..."
    firebase init database
else
    echo "✅ firebase.json found"
fi

echo "🔍 Validating database rules..."
if [ -f "database.rules.json" ]; then
    echo "✅ database.rules.json found"

    if python -m json.tool database.rules.json > /dev/null 2>&1; then
        echo "✅ Rules file is valid JSON"
    else
        echo "❌ Rules file has invalid JSON syntax"
        exit 1
    fi
else
    echo "❌ database.rules.json not found"
    exit 1
fi

echo "🚀 Deploying database rules..."
firebase deploy --only database

if [ $? -eq 0 ]; then
    echo "✅ Database rules deployed successfully!"
    echo ""
    echo "📊 Next steps:"
    echo "1. Check Firebase Console > Realtime Database > Rules"
    echo "2. Verify indexes are applied"
    echo "3. Test the app to ensure no more index errors"
    echo ""
    echo "🔗 Firebase Console: https://console.firebase.google.com/"
else
    echo "❌ Failed to deploy database rules"
    echo "💡 Try deploying manually from Firebase Console"
    exit 1
fi

echo "🎉 Setup complete!"

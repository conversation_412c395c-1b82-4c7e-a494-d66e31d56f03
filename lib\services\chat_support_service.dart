import 'package:firebase_database/firebase_database.dart';
import '../models/chat_support_model.dart';

class ChatSupportService {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final String _collection = 'chat_support';

  
  Future<List<ChatSupportModel>> getAllChats() async {
    try {
      final snapshot = await _database.ref(_collection).get();
      
      if (!snapshot.exists) return [];

      final chatsData = snapshot.value as Map<dynamic, dynamic>;
      final chats = <ChatSupportModel>[];

      chatsData.forEach((chatId, chatData) {
        chats.add(ChatSupportModel.fromJson(
          chatId.toString(),
          Map<String, dynamic>.from(chatData as Map),
        ));
      });

      
      chats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      return chats;
    } catch (e) {
      throw Exception('Failed to get chats: $e');
    }
  }

  
  Future<List<ChatSupportModel>> getChatsByUser(String userId) async {
    try {
      final snapshot = await _database.ref(_collection).get();
      
      if (!snapshot.exists) return [];

      final chatsData = snapshot.value as Map<dynamic, dynamic>;
      final userChats = <ChatSupportModel>[];

      chatsData.forEach((chatId, chatData) {
        final chat = ChatSupportModel.fromJson(
          chatId.toString(),
          Map<String, dynamic>.from(chatData as Map),
        );
        
        if (chat.isParticipant(userId)) {
          userChats.add(chat);
        }
      });

      
      userChats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      return userChats;
    } catch (e) {
      throw Exception('Failed to get chats by user: $e');
    }
  }

  
  Future<ChatSupportModel?> getChatById(String chatId) async {
    try {
      final snapshot = await _database.ref('$_collection/$chatId').get();
      
      if (snapshot.exists) {
        return ChatSupportModel.fromJson(
          chatId,
          Map<String, dynamic>.from(snapshot.value as Map),
        );
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  
  Future<ChatSupportModel> createChat({
    required List<String> participants,
    required ChatType type,
  }) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final chatRef = _database.ref(_collection).push();
      
      final chatData = {
        'participants': participants,
        'type': type.name,
        'status': ChatStatus.active.name,
        'createdAt': now,
        'updatedAt': now,
        'messages': {},
      };

      await chatRef.set(chatData);

      return ChatSupportModel.fromJson(chatRef.key!, chatData);
    } catch (e) {
      throw Exception('Failed to create chat: $e');
    }
  }

  
  Future<ChatMessageModel> sendMessage({
    required String chatId,
    required String senderId,
    required String senderName,
    required String message,
    MessageType type = MessageType.text,
  }) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final messageRef = _database.ref('$_collection/$chatId/messages').push();
      
      final messageData = {
        'senderId': senderId,
        'senderName': senderName,
        'message': message,
        'type': type.name,
        'timestamp': now,
        'isRead': false,
      };

      await messageRef.set(messageData);

      
      await _database.ref('$_collection/$chatId/updatedAt').set(now);

      return ChatMessageModel.fromJson(messageRef.key!, messageData);
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  
  Future<void> markMessageAsRead(String chatId, String messageId) async {
    try {
      await _database
          .ref('$_collection/$chatId/messages/$messageId/isRead')
          .set(true);
    } catch (e) {
      throw Exception('Failed to mark message as read: $e');
    }
  }

  
  Future<void> markAllMessagesAsRead(String chatId, String userId) async {
    try {
      final chat = await getChatById(chatId);
      if (chat == null) return;

      final updates = <String, dynamic>{};
      
      chat.messages.forEach((messageId, message) {
        if (message.senderId != userId && !message.isRead) {
          updates['$_collection/$chatId/messages/$messageId/isRead'] = true;
        }
      });

      if (updates.isNotEmpty) {
        await _database.ref().update(updates);
      }
    } catch (e) {
      throw Exception('Failed to mark all messages as read: $e');
    }
  }

  
  Future<void> closeChat(String chatId) async {
    try {
      await _database.ref('$_collection/$chatId').update({
        'status': ChatStatus.closed.name,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to close chat: $e');
    }
  }

  
  Future<void> reopenChat(String chatId) async {
    try {
      await _database.ref('$_collection/$chatId').update({
        'status': ChatStatus.active.name,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to reopen chat: $e');
    }
  }

  
  Future<void> deleteChat(String chatId) async {
    try {
      await _database.ref('$_collection/$chatId').remove();
    } catch (e) {
      throw Exception('Failed to delete chat: $e');
    }
  }

  
  Stream<List<ChatSupportModel>> getChatsStream() {
    return _database.ref(_collection).onValue.map((event) {
      if (!event.snapshot.exists) return <ChatSupportModel>[];

      final chatsData = event.snapshot.value as Map<dynamic, dynamic>;
      final chats = <ChatSupportModel>[];

      chatsData.forEach((chatId, chatData) {
        chats.add(ChatSupportModel.fromJson(
          chatId.toString(),
          Map<String, dynamic>.from(chatData as Map),
        ));
      });

      
      chats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      return chats;
    });
  }

  Stream<List<ChatSupportModel>> getChatsByUserStream(String userId) {
    return _database.ref(_collection).onValue.map((event) {
      if (!event.snapshot.exists) return <ChatSupportModel>[];

      final chatsData = event.snapshot.value as Map<dynamic, dynamic>;
      final userChats = <ChatSupportModel>[];

      chatsData.forEach((chatId, chatData) {
        final chat = ChatSupportModel.fromJson(
          chatId.toString(),
          Map<String, dynamic>.from(chatData as Map),
        );
        
        if (chat.isParticipant(userId)) {
          userChats.add(chat);
        }
      });

      
      userChats.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      return userChats;
    });
  }

  Stream<ChatSupportModel?> getChatStream(String chatId) {
    return _database.ref('$_collection/$chatId').onValue.map((event) {
      if (event.snapshot.exists) {
        return ChatSupportModel.fromJson(
          chatId,
          Map<String, dynamic>.from(event.snapshot.value as Map),
        );
      }
      return null;
    });
  }

  Stream<List<ChatMessageModel>> getMessagesStream(String chatId) {
    return _database.ref('$_collection/$chatId/messages').onValue.map((event) {
      if (!event.snapshot.exists) return <ChatMessageModel>[];

      final messagesData = event.snapshot.value as Map<dynamic, dynamic>;
      final messages = <ChatMessageModel>[];

      messagesData.forEach((messageId, messageData) {
        messages.add(ChatMessageModel.fromJson(
          messageId.toString(),
          Map<String, dynamic>.from(messageData as Map),
        ));
      });

      
      messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      return messages;
    });
  }

  
  Future<List<ChatSupportModel>> getActiveSupportChats() async {
    try {
      final allChats = await getAllChats();
      return allChats
          .where((chat) => chat.isActive && chat.isUserSupport)
          .toList();
    } catch (e) {
      throw Exception('Failed to get active support chats: $e');
    }
  }

  
  Future<List<ChatSupportModel>> getBugReportChats() async {
    try {
      final allChats = await getAllChats();
      return allChats
          .where((chat) => chat.isBugReport)
          .toList();
    } catch (e) {
      throw Exception('Failed to get bug report chats: $e');
    }
  }

  
  Future<int> getUnreadCountForUser(String userId) async {
    try {
      final userChats = await getChatsByUser(userId);
      int totalUnread = 0;
      
      for (final chat in userChats) {
        totalUnread += chat.getUnreadCountForUser(userId);
      }
      
      return totalUnread;
    } catch (e) {
      throw Exception('Failed to get unread count: $e');
    }
  }

  
  Future<List<ChatSupportModel>> searchChats(String query) async {
    try {
      final allChats = await getAllChats();
      final searchQuery = query.toLowerCase();
      
      return allChats.where((chat) {
        
        for (final message in chat.messagesList) {
          if (message.message.toLowerCase().contains(searchQuery) ||
              message.senderName.toLowerCase().contains(searchQuery)) {
            return true;
          }
        }
        return false;
      }).toList();
    } catch (e) {
      throw Exception('Failed to search chats: $e');
    }
  }

  
  Future<Map<String, dynamic>> getChatStatistics() async {
    try {
      final allChats = await getAllChats();
      
      int totalChats = allChats.length;
      int activeChats = 0;
      int closedChats = 0;
      int supportChats = 0;
      int bugReportChats = 0;
      int totalMessages = 0;
      
      for (final chat in allChats) {
        if (chat.isActive) {
          activeChats++;
        } else {
          closedChats++;
        }
        
        if (chat.isUserSupport) {
          supportChats++;
        } else if (chat.isBugReport) {
          bugReportChats++;
        }
        
        totalMessages += chat.messagesList.length;
      }

      return {
        'totalChats': totalChats,
        'activeChats': activeChats,
        'closedChats': closedChats,
        'supportChats': supportChats,
        'bugReportChats': bugReportChats,
        'totalMessages': totalMessages,
        'averageMessagesPerChat': totalChats > 0 ? totalMessages / totalChats : 0,
      };
    } catch (e) {
      throw Exception('Failed to get chat statistics: $e');
    }
  }
}

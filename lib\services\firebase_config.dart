

import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;
import 'debug_firebase_config.dart';

class FirebaseConfig {
  
  
  static Future<void> initializeAppCheck() async {
    try {
      
      
      if (kDebugMode) {
        print(
            'Skipping Firebase App Check in debug mode to avoid attestation errors');
        return;
      }

      
      
      await FirebaseAppCheck.instance.activate(
        webProvider: ReCaptchaV3Provider('your-recaptcha-site-key'),
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.deviceCheck,
      );

      if (kDebugMode) {
        print('Firebase App Check initialized successfully');
      }
    } catch (e) {
      
      
      if (kDebugMode) {
        print('Firebase App Check initialization failed: $e');
      }
    }
  }

  
  
  static void configureFirebaseLocale() {
    if (kDebugMode) {
      
      
      DebugFirebaseConfig.configureForDebug();
      DebugFirebaseConfig.disableProblematicFeatures();
      return;
    }

    try {
      
      
      final locale = ui.PlatformDispatcher.instance.locale;
      FirebaseAuth.instance.setLanguageCode(locale.languageCode);

      if (kDebugMode) {
        print('Firebase locale set to: ${locale.languageCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Firebase locale configuration failed: $e');
      }
    }
  }

  
  
  static Future<void> configureFirebaseAuth() async {
    try {
      
      
      FirebaseAuth.instance.authStateChanges().listen((User? user) {
        if (kDebugMode) {
          print('Firebase Auth state changed: ${user?.email ?? 'No user'}');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Firebase Auth configuration failed: $e');
      }
    }
  }
}

import 'package:shared_preferences/shared_preferences.dart';

class StorageCleanup {
  
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      
    }
  }

  
  static Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      
      if (prefs.containsKey('user')) {
        await prefs.remove('user');
      }
      if (prefs.containsKey('isLoggedIn')) {
        await prefs.remove('isLoggedIn');
      }
    } catch (e) {
      
    }
  }

  
  static Future<void> checkAndFixProblematicData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      
      final allKeys = prefs.getKeys();
      for (final key in allKeys) {
        try {
          
          prefs.get(key);
        } catch (e) {
          
          await prefs.remove(key);
        }
      }
    } catch (e) {
      
      await clearAllData();
    }
  }
}

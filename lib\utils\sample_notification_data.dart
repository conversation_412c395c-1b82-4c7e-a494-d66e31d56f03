import '../services/realtime_database_service.dart';

class SampleNotificationData {
  static final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();

  static Future<void> createSampleNotifications() async {
    try {
      print('Creating sample notifications...');

      
      await _realtimeService.createNotification(
        title: 'Chào mừng đến với Đớp Phim!',
        body: 'Cảm ơn bạn đã sử dụng ứng dụng đặt vé xem phim của chúng tôi. Hãy khám phá những bộ phim hot nhất hiện tại!',
        type: 'system',
        priority: 'normal',
        isPublic: true,
        expiresIn: const Duration(days: 30),
      );

      await _realtimeService.createNotification(
        title: 'Cập nhật tính năng mới',
        body: 'Chúng tôi đã thêm tính năng thông báo realtime để bạn không bỏ lỡ bất kỳ thông tin quan trọng nào!',
        type: 'system',
        priority: 'high',
        isPublic: true,
        expiresIn: const Duration(days: 7),
      );

      
      await _realtimeService.createNotification(
        title: 'Phim mới: Avatar 3 - The Seed Bearer',
        body: 'Bom tấn được mong đợi nhất năm 2024 đã có lịch chiếu! Đặt vé ngay để không bỏ lỡ.',
        type: 'movie',
        priority: 'high',
        isPublic: true,
        targetScreen: '/movie_detail',
        data: {
          'movieId': 'avatar3',
          'movieTitle': 'Avatar 3 - The Seed Bearer',
        },
        expiresIn: const Duration(days: 14),
      );

      await _realtimeService.createNotification(
        title: 'Phim hot: Dune: Part Two',
        body: 'Tiếp nối hành trình huyền thoại của Paul Atreides. Suất chiếu đặc biệt với âm thanh IMAX.',
        type: 'movie',
        priority: 'normal',
        isPublic: true,
        targetScreen: '/movie_detail',
        data: {
          'movieId': 'dune2',
          'movieTitle': 'Dune: Part Two',
        },
        expiresIn: const Duration(days: 10),
      );

      
      await _realtimeService.createNotification(
        title: '🎉 Giảm giá 50% vé xem phim cuối tuần!',
        body: 'Áp dụng cho tất cả suất chiếu từ thứ 6 đến chủ nhật. Sử dụng mã: WEEKEND50',
        type: 'promo',
        priority: 'urgent',
        isPublic: true,
        targetScreen: '/promo_detail',
        data: {
          'promoCode': 'WEEKEND50',
          'discount': '50',
          'validUntil': DateTime.now().add(const Duration(days: 3)).millisecondsSinceEpoch.toString(),
        },
        expiresIn: const Duration(days: 3),
      );

      await _realtimeService.createNotification(
        title: 'Combo bắp nước chỉ 99k',
        body: 'Combo bắp rang bơ + nước ngọt size L chỉ với 99.000đ. Áp dụng khi mua vé online.',
        type: 'promo',
        priority: 'normal',
        isPublic: true,
        expiresIn: const Duration(days: 7),
      );

      
      await _realtimeService.createNotification(
        title: 'Nhắc nhở: Vé của bạn sắp hết hạn',
        body: 'Bạn có 2 vé xem phim sẽ hết hạn trong 24 giờ tới. Vui lòng kiểm tra và sử dụng kịp thời.',
        type: 'ticket',
        priority: 'high',
        isPublic: false,
        targetScreen: '/my_tickets',
        expiresIn: const Duration(days: 1),
      );

      
      await _realtimeService.createNotification(
        title: '⚠️ Thông báo khẩn cấp: Bảo trì hệ thống',
        body: 'Hệ thống sẽ bảo trì từ 2:00 - 4:00 sáng ngày mai. Vui lòng hoàn tất giao dịch trước thời gian này.',
        type: 'system',
        priority: 'urgent',
        isPublic: true,
        expiresIn: const Duration(hours: 12),
      );

      
      await _realtimeService.createNotification(
        title: 'Mẹo xem phim: Đến sớm 15 phút',
        body: 'Để có trải nghiệm tốt nhất, bạn nên đến rạp trước 15 phút so với giờ chiếu để làm thủ tục và tìm chỗ ngồi.',
        type: 'system',
        priority: 'low',
        isPublic: true,
        expiresIn: const Duration(days: 30),
      );

      
      await _realtimeService.createNotification(
        title: 'Khai trương rạp mới tại Quận 7',
        body: 'CGV Crescent Mall chính thức khai trương với 8 phòng chiếu hiện đại và công nghệ âm thanh Dolby Atmos.',
        imageUrl: 'https://example.com/cgv-crescent-mall.jpg',
        type: 'system',
        priority: 'normal',
        isPublic: true,
        targetScreen: '/theater_detail',
        data: {
          'theaterId': 'cgv-crescent-mall',
          'theaterName': 'CGV Crescent Mall',
        },
        expiresIn: const Duration(days: 14),
      );

      print('Sample notifications created successfully!');
    } catch (e) {
      print('Error creating sample notifications: $e');
    }
  }

  static Future<void> createAdminNotifications() async {
    try {
      print('Creating admin notifications...');

      
      await _realtimeService.createNotification(
        title: 'Báo cáo doanh thu tuần',
        body: 'Doanh thu tuần này đạt 2.5 tỷ VNĐ, tăng 15% so với tuần trước. Xem chi tiết trong báo cáo.',
        type: 'system',
        priority: 'normal',
        isPublic: false,
        targetScreen: '/admin/revenue_report',
        data: {
          'reportType': 'weekly',
          'revenue': '2500000000',
          'growth': '15',
        },
        expiresIn: const Duration(days: 7),
      );

      await _realtimeService.createNotification(
        title: 'Cảnh báo: Lỗi hệ thống thanh toán',
        body: 'Phát hiện 5 giao dịch thanh toán thất bại trong 1 giờ qua. Cần kiểm tra ngay.',
        type: 'system',
        priority: 'urgent',
        isPublic: false,
        targetScreen: '/admin/payment_issues',
        data: {
          'failedTransactions': '5',
          'timeframe': '1hour',
        },
        expiresIn: const Duration(hours: 6),
      );

      await _realtimeService.createNotification(
        title: 'Yêu cầu phê duyệt: Thêm phim mới',
        body: 'Có 3 bộ phim mới đang chờ phê duyệt để thêm vào hệ thống. Vui lòng xem xét.',
        type: 'system',
        priority: 'normal',
        isPublic: false,
        targetScreen: '/admin/movie_approval',
        data: {
          'pendingMovies': '3',
        },
        expiresIn: const Duration(days: 3),
      );

      print('Admin notifications created successfully!');
    } catch (e) {
      print('Error creating admin notifications: $e');
    }
  }

  static Future<void> createDeveloperNotifications() async {
    try {
      print('Creating developer notifications...');

      
      await _realtimeService.createNotification(
        title: 'Bug Report: Lỗi đặt vé trùng lặp',
        body: 'Phát hiện lỗi cho phép đặt cùng một ghế nhiều lần. Cần fix ngay.',
        type: 'bug_report',
        priority: 'urgent',
        isPublic: false,
        targetScreen: '/bug_report_detail',
        data: {
          'bugId': 'BUG-001',
          'severity': 'critical',
        },
        expiresIn: const Duration(days: 1),
      );

      await _realtimeService.createNotification(
        title: 'Deployment: Version 2.1.0 thành công',
        body: 'Phiên bản 2.1.0 đã được deploy thành công lên production. Tất cả tính năng hoạt động bình thường.',
        type: 'system',
        priority: 'normal',
        isPublic: false,
        targetScreen: '/admin/deployment_log',
        data: {
          'version': '2.1.0',
          'deployTime': DateTime.now().millisecondsSinceEpoch.toString(),
        },
        expiresIn: const Duration(days: 7),
      );

      print('Developer notifications created successfully!');
    } catch (e) {
      print('Error creating developer notifications: $e');
    }
  }

  static Future<void> createAllSampleData() async {
    await createSampleNotifications();
    await createAdminNotifications();
    await createDeveloperNotifications();
    print('All sample notification data created!');
  }
}

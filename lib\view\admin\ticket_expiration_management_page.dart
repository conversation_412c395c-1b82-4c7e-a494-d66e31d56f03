import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/ticket_expiration_service.dart';
import '../../controllers/ticket_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/app_colors.dart';
import '../../utils/create_test_expired_ticket.dart';

class TicketExpirationManagementPage extends StatefulWidget {
  const TicketExpirationManagementPage({Key? key}) : super(key: key);

  @override
  State<TicketExpirationManagementPage> createState() =>
      _TicketExpirationManagementPageState();
}

class _TicketExpirationManagementPageState
    extends State<TicketExpirationManagementPage> {
  final TicketExpirationService _expirationService =
      Get.find<TicketExpirationService>();
  final TicketController _ticketController = Get.find<TicketController>();
  final AuthController _authController = Get.find<AuthController>();

  Map<String, dynamic>? _expirationStatus;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadExpirationStatus();
  }

  Future<void> _loadExpirationStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final status = await _expirationService.getExpirationStatus();
      setState(() {
        _expirationStatus = status;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải trạng thái: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _forceCheckExpiredTickets() async {
    setState(() {
      _isLoading = true;
    });

    try {
      
      try {
        final result =
            await _expirationService.updateExpiredTicketsServerSide();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Đã cập nhật ${result['updatedCount']} vé hết hạn từ server'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      } catch (serverError) {
        print(
            'Server-side update failed, falling back to client-side: $serverError');

        
        await _expirationService.forceCheckExpiredTickets();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Đã kiểm tra và cập nhật vé hết hạn thành công (client-side)'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      }

      await _ticketController.checkExpiredTickets();
      await _loadExpirationStatus();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi kiểm tra vé hết hạn: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _forceServerSideUpdate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _expirationService.updateExpiredTicketsServerSide();
      await _loadExpirationStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Server đã cập nhật ${result['updatedCount']} vé hết hạn'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi gọi server: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestExpiredTicket() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authController.user?.id ?? 'test_user';
      final ticketId = await TestExpiredTicketCreator.createExpiredTestTicket(
        userId: userId,
        movieTitle: 'Test Expired Movie',
        daysAgo: 1,
        hoursAgo: 2,
      );

      await _loadExpirationStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã tạo vé test hết hạn: $ticketId'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tạo vé test: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createSoonToExpireTicket() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authController.user?.id ?? 'test_user';
      final ticketId =
          await TestExpiredTicketCreator.createSoonToExpireTestTicket(
        userId: userId,
        minutesFromNow: 2,
      );

      await _loadExpirationStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã tạo vé sẽ hết hạn sau 2 phút: $ticketId'),
            backgroundColor: AppColors.primaryAmber,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tạo vé test: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _cleanupTestTickets() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await TestExpiredTicketCreator.cleanupTestTickets();
      await _loadExpirationStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã xóa tất cả vé test'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi xóa vé test: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quản lý vé hết hạn'),
        backgroundColor: AppColors.primaryBlue,
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatusCard(),
                    const SizedBox(height: 20),
                    _buildActionsCard(),
                    const SizedBox(height: 20),
                    _buildInfoCard(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStatusCard() {
    if (_expirationStatus == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('Chưa có dữ liệu trạng thái'),
        ),
      );
    }

    final status = _expirationStatus!;
    final hasError = status.containsKey('error');

    return Card(
      color: AppColors.cardBackground,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Trạng thái vé',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            if (hasError) ...[
              Text(
                'Lỗi: ${status['error']}',
                style: const TextStyle(color: AppColors.errorRed),
              ),
            ] else ...[
              _buildStatusRow('Vé đã xác nhận', '${status['totalConfirmed']}'),
              _buildStatusRow('Vé đã hết hạn', '${status['totalExpired']}'),
              _buildStatusRow(
                'Vé cần cập nhật hết hạn',
                '${status['shouldBeExpired']}',
                isWarning: status['shouldBeExpired'] > 0,
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'Lần kiểm tra cuối: ${_formatDateTime(status['lastCheck'])}',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, {bool isWarning = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: AppColors.textPrimary),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color:
                  isWarning ? AppColors.warningOrange : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      color: AppColors.cardBackground,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Hành động',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _forceCheckExpiredTickets,
                icon: const Icon(Icons.refresh),
                label: const Text('Kiểm tra vé hết hạn ngay'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: AppColors.textPrimary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _forceServerSideUpdate,
                icon: const Icon(Icons.cloud_sync),
                label: const Text('Cập nhật từ server'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryAmber,
                  foregroundColor: AppColors.textPrimary,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _loadExpirationStatus,
                icon: const Icon(Icons.update),
                label: const Text('Làm mới trạng thái'),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Test Functions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _createTestExpiredTicket,
                icon: const Icon(Icons.add_alarm),
                label: const Text('Tạo vé test đã hết hạn'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.errorRed,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _createSoonToExpireTicket,
                icon: const Icon(Icons.timer),
                label: const Text('Tạo vé sẽ hết hạn (2 phút)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warningOrange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _cleanupTestTickets,
                icon: const Icon(Icons.delete_sweep),
                label: const Text('Xóa tất cả vé test'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.errorRed,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return const Card(
      color: AppColors.cardBackground,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 12),
            Text(
              '• Hệ thống tự động kiểm tra vé hết hạn mỗi 10 phút',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            SizedBox(height: 4),
            Text(
              '• Vé sẽ được đánh dấu hết hạn khi quá ngày/giờ chiếu',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            SizedBox(height: 4),
            Text(
              '• Vé hết hạn không thể sử dụng hoặc hủy',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(String? dateTimeString) {
    if (dateTimeString == null) return 'Chưa có';

    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Không hợp lệ';
    }
  }
}

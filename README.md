

Ứng dụng Flutter toàn diện cho việc đặt vé xem phim với các tính năng nâng cao bao gồm thông báo thời gian thực, xác thực phân quyền và tích hợp thanh toán.

- **Khám Phá Phim**: <PERSON><PERSON><PERSON><PERSON> phim với thông tin chi tiết, trailer và đánh giá
- **Đặt Vé**: <PERSON>ệ thống đặt vé hoàn chỉnh với chọn ghế và tình trạng thời gian thực
- **Xác Thực Người Dùng**: <PERSON><PERSON><PERSON> thực dựa trên Firebase với hỗ trợ đăng nhập Google
- **Phân Quyền**: <PERSON><PERSON><PERSON> cấp độ truy cập khác nhau cho người dùng, admin và developer
- **Tích Hợp Thanh <PERSON>**: <PERSON><PERSON><PERSON> hợ<PERSON> Pay<PERSON>al sandbox để thanh toán an toàn
- **Thông Báo Thời Gian Thực**: <PERSON><PERSON> thống thông báo trực tiếp cho đặt vé và cập nhật

- **<PERSON>uản Lý Lịch Chiếu**: Quản lý lịch chiếu phim toàn diện với rạp và phòng chiếu
- **Bảng Điều Khiển Admin**: Quản lý rạp, phòng chiếu và phim cho quản trị viên
- **Nhập Hàng Loạt**: Chức năng nhập Excel/CSV cho phim, rạp và lịch chiếu
- **Vé QR Code**: Vé điện tử với mã QR để xác minh dễ dàng
- **Hết Hạn Tự Động**: Xử lý hết hạn vé phía máy chủ
- **Bảo Vệ Đặt Vé Đồng Thời**: Ngăn chặn xung đột đặt vé trùng lặp
- **Báo Cáo Lỗi**: Hệ thống báo cáo lỗi tích hợp với thông báo admin

- **Màn Hình Chào**: Banner phim tự cuộn khi khởi động ứng dụng
- **Điều Hướng Thể Loại**: Giao diện tiếng Việt với tích hợp backend tiếng Anh
- **Chức Năng Tìm Kiếm**: Tìm kiếm phim theo tên với hiệu suất tối ưu
- **Phim Yêu Thích**: Bộ sưu tập phim cá nhân với chức năng tải lại
- **Quản Lý Hồ Sơ**: Tải avatar lên Firebase Storage với thay đổi tên hiển thị
- **Hệ Thống Trợ Giúp**: Chat hỗ trợ trực tiếp và gửi ticket hỗ trợ

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Realtime Database, Authentication, Storage, Functions)
- **Thanh Toán**: PayPal SDK
- **Trình Phát Video**: Media Kit để phát trailer tối ưu
- **Quản Lý Trạng Thái**: GetX
- **Thành Phần UI**: Material Design với theme tùy chỉnh

<img src="assets/project_progress/279225706_552121216268632_8462016636129319895_n.png" width=270 alt="Trang chủ ứng dụng Đớp Phim" >

<img src="assets/project_progress/280654967_1984518965082018_8662307702767337035_n.png" width=270 alt="Trang chi tiết phim" >

- Flutter SDK (>=2.16.2 <3.0.0)
- Android Studio / VS Code
- Dự án Firebase đã thiết lập
- Tài khoản PayPal developer (để test thanh toán)

1. **Clone repository**
   ```bash
   git clone https://github.com/yourusername/dop-phim-flutter.git
   cd dop-phim-flutter
   ```

2. **Cài đặt dependencies**
   ```bash
   flutter pub get
   ```

3. **Thiết lập Firebase**
   - Tạo dự án Firebase mới
   - Bật Authentication, Firestore, Realtime Database, Storage và Functions
   - Tải xuống `google-services.json` (Android) và `GoogleService-Info.plist` (iOS)
   - Đặt các file cấu hình vào thư mục phù hợp
   - Cập nhật Firebase security rules sử dụng các file rules có sẵn

4. **Cấu hình Firebase Functions**
   ```bash
   cd functions
   npm install
   firebase deploy --only functions
   ```

5. **Thiết lập PayPal**
   - Tạo tài khoản PayPal developer
   - Cấu hình sandbox credentials
   - Cập nhật cấu hình thanh toán trong ứng dụng

6. **Chạy ứng dụng**
   ```bash
   flutter run
   ```

Dự án bao gồm các security rules đã được cấu hình sẵn cho:
- Firestore (`firestore.rules`)
- Realtime Database (`database.rules.json`)
- Firebase Storage (`storage.rules`)

Deploy rules bằng lệnh:
```bash
firebase deploy --only firestore:rules,database,storage
```

Tạo các file cấu hình phù hợp cho:
- API keys
- Cấu hình Firebase
- Thông tin đăng nhập PayPal
- Dữ liệu nhạy cảm khác

```
lib/
├── bindings/          # GetX bindings
├── config/           # Cấu hình ứng dụng
├── controllers/      # GetX controllers
├── models/          # Data models
├── services/        # API và Firebase services
├── utils/           # Utility functions
├── view/            # UI screens
├── widgets/         # Reusable widgets
└── translations/    # Đa ngôn ngữ
```

- Firebase Authentication với kiểm soát truy cập dựa trên vai trò
- Xử lý thanh toán an toàn thông qua PayPal
- Xác thực và làm sạch dữ liệu
- Bảo vệ các route và function admin
- Lưu trữ dữ liệu nhạy cảm được mã hóa

Chạy tests bằng lệnh:
```bash
flutter test
```

Cho các file test cụ thể:
```bash
flutter test test/ticket_detail_test.dart
```

Tài liệu bổ sung có sẵn:
- [Hướng dẫn thiết lập Firebase](FIREBASE_STORAGE_AVATAR_README.md)
- [Hướng dẫn tích hợp PayPal](PAYPAL_INTEGRATION_GUIDE.md)
- [Hướng dẫn Security Rules](SECURITY_RULES_GUIDE.md)
- [Hướng dẫn chức năng Import](IMPORT_GUIDE.md)
- [Hướng dẫn hệ thống thông báo](REALTIME_NOTIFICATION_SYSTEM.md)

Vui lòng đọc [CONTRIBUTING.md](CONTRIBUTING.md) để biết chi tiết về quy tắc ứng xử và quy trình gửi pull requests.

Dự án này được cấp phép theo MIT License - xem file [LICENSE](LICENSE) để biết chi tiết.

- Đội ngũ Flutter vì framework tuyệt vời
- Firebase cho các dịch vụ backend
- PayPal cho tích hợp thanh toán
- Tất cả contributors và testers

Để được hỗ trợ và đặt câu hỏi:
- Tạo issue trong repository này
- Sử dụng hệ thống trợ giúp trong ứng dụng
- Liên hệ với đội ngũ phát triển

- ✅ Đặt vé xem phim dễ dàng với giao diện thân thiện
- ✅ Xem trailer phim chất lượng cao
- ✅ Thanh toán an toàn qua PayPal
- ✅ Nhận thông báo thời gian thực
- ✅ Quản lý vé và lịch sử đặt vé

- ✅ Quản lý phim, rạp và lịch chiếu
- ✅ Nhập dữ liệu hàng loạt từ Excel/CSV
- ✅ Theo dõi doanh thu và thống kê
- ✅ Hệ thống báo cáo lỗi từ người dùng
- ✅ Quản lý người dùng và phân quyền

- ✅ Kiến trúc code sạch và dễ bảo trì
- ✅ Tích hợp Firebase hoàn chỉnh
- ✅ CI/CD pipeline tự động
- ✅ Tài liệu chi tiết và đầy đủ
- ✅ Security rules được cấu hình sẵn

---

**Lưu ý**: Ứng dụng này được thiết kế cho mục đích giáo dục và demo. Hãy đảm bảo các biện pháp bảo mật phù hợp và tuân thủ quy định địa phương trước khi triển khai production.

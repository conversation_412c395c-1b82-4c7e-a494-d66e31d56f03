import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TestingPage extends StatelessWidget {
  const TestingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MainContent(),
    );
  }
}

class MainContent extends StatelessWidget {
  MainContent({Key? key}) : super(key: key);

  var dragDirection = ''.obs;
  var startDXPoint = 0.0.obs;
  var startDYPoint = 0.0.obs;
  var velocity = 0.0.obs;

  @override
  Widget build(BuildContext context) {
    
    
    
    
    

    
    
    
    
    
    

    
    void onDragUpdateHandler(DragUpdateDetails details) {
      dragDirection.value = "UPDATING";
      startDXPoint.value = details.globalPosition.dx.floorToDouble();
      startDYPoint.value = details.globalPosition.dy.floorToDouble();
    }

    
    void onDragEnd(DragEndDetails details) {
      velocity.value =
          details.velocity.pixelsPerSecond.dx.abs().floorToDouble();
    }

    return GestureDetector(
      
      
      onVerticalDragUpdate: onDragUpdate<PERSON><PERSON><PERSON>,
      onVerticalDragEnd: onDragEnd,
      child: Container(
        color: Colors.grey[100],
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Obx(
                () => Text(
                  dragDirection.value,
                  style: const TextStyle(
                      fontSize: 55,
                      fontWeight: FontWeight.w700,
                      color: Colors.black),
                ),
              ),
              Obx(
                () => Text(
                  'Start DX point: ${startDXPoint.value}',
                  style: const TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Obx(
                () => Text(
                  'Start DY point: ${startDYPoint.value}',
                  style: const TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Obx(() => Text(
                    'Velocity: ${velocity.value}',
                    style: const TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.w500,
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}

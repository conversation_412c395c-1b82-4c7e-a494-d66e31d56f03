import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/showtime_model.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/screen_model.dart';

class TimeSlot {
  final String startTime;
  final String endTime;
  final DateTime startDateTime;
  final DateTime endDateTime;

  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.startDateTime,
    required this.endDateTime,
  });

  bool overlapsWith(TimeSlot other) {
    return startDateTime.isBefore(other.endDateTime) &&
        endDateTime.isAfter(other.startDateTime);
  }

  Duration get duration => endDateTime.difference(startDateTime);

  String get displayTime => '$startTime - $endTime';
}

class ScheduleConflict {
  final String conflictType; 
  final String message;
  final ShowtimeModel? conflictingShowtime;
  final List<String> suggestedTimes;

  ScheduleConflict({
    required this.conflictType,
    required this.message,
    this.conflictingShowtime,
    this.suggestedTimes = const [],
  });
}

class ScheduleService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'showtimes';

  
  static const Duration _minimumGapBetweenShows = Duration(minutes: 30);

  
  static const Map<String, Duration> _standardDurations = {
    'short': Duration(minutes: 90),
    'standard': Duration(minutes: 120),
    'long': Duration(minutes: 150),
    'epic': Duration(minutes: 180),
  };

  
  Future<ScheduleConflict?> checkTimeSlotAvailability({
    required String theaterId,
    required String screenId,
    required String date,
    required String startTime,
    required Duration movieDuration,
    String? excludeShowtimeId,
  }) async {
    try {
      
      final startDateTime = _parseDateTime(date, startTime);
      final endDateTime =
          startDateTime.add(movieDuration).add(_minimumGapBetweenShows);
      final endTime = _formatTime(endDateTime);

      
      final existingShowtimes = await _getShowtimesForScreenAndDate(
          theaterId, screenId, date, excludeShowtimeId);

      
      final proposedSlot = TimeSlot(
        startTime: startTime,
        endTime: endTime,
        startDateTime: startDateTime,
        endDateTime: endDateTime,
      );

      for (final showtime in existingShowtimes) {
        final existingSlot = TimeSlot(
          startTime: showtime.time,
          endTime: showtime.endTime,
          startDateTime: showtime.showDateTime,
          endDateTime: showtime.endDateTime,
        );

        if (proposedSlot.overlapsWith(existingSlot)) {
          final suggestedTimes = await _generateSuggestedTimes(
              theaterId, screenId, date, movieDuration, excludeShowtimeId);

          return ScheduleConflict(
            conflictType: 'overlap',
            message:
                'Khung giờ này đã có lịch chiếu khác (${showtime.time} - ${showtime.endTime})',
            conflictingShowtime: showtime,
            suggestedTimes: suggestedTimes,
          );
        }
      }

      

      return null; 
    } catch (e) {
      throw Exception('Failed to check time slot availability: $e');
    }
  }

  
  Future<List<String>> _generateSuggestedTimes(
    String theaterId,
    String screenId,
    String date,
    Duration movieDuration,
    String? excludeShowtimeId,
  ) async {
    final existingShowtimes = await _getShowtimesForScreenAndDate(
        theaterId, screenId, date, excludeShowtimeId);

    
    existingShowtimes.sort((a, b) => a.showDateTime.compareTo(b.showDateTime));

    List<String> suggestions = [];

    
    final operatingStart = DateTime.parse('$date 09:00:00');
    final operatingEnd = DateTime.parse('$date 23:00:00');

    DateTime currentTime = operatingStart;

    
    for (int i = 0; i <= existingShowtimes.length; i++) {
      DateTime? nextShowStart;

      if (i < existingShowtimes.length) {
        nextShowStart = existingShowtimes[i].showDateTime;
      } else {
        nextShowStart = operatingEnd;
      }

      
      final requiredDuration = movieDuration + _minimumGapBetweenShows;

      if (currentTime.add(requiredDuration).isBefore(nextShowStart) ||
          currentTime.add(requiredDuration).isAtSameMomentAs(nextShowStart)) {
        suggestions.add(_formatTime(currentTime));

        
        DateTime nextSuggestion = currentTime.add(const Duration(minutes: 30));
        while (nextSuggestion.add(requiredDuration).isBefore(nextShowStart) ||
            nextSuggestion
                .add(requiredDuration)
                .isAtSameMomentAs(nextShowStart)) {
          suggestions.add(_formatTime(nextSuggestion));
          nextSuggestion = nextSuggestion.add(const Duration(minutes: 30));

          if (suggestions.length >= 5) break; 
        }
      }

      if (i < existingShowtimes.length) {
        currentTime =
            existingShowtimes[i].endDateTime.add(_minimumGapBetweenShows);
      }

      if (suggestions.length >= 5) break; 
    }

    return suggestions;
  }

  
  Future<List<ShowtimeModel>> _getShowtimesForScreenAndDate(
    String theaterId,
    String screenId,
    String date,
    String? excludeShowtimeId,
  ) async {
    Query query = _firestore
        .collection(_collection)
        .where('theaterId', isEqualTo: theaterId)
        .where('screenId', isEqualTo: screenId)
        .where('date', isEqualTo: date)
        .where('status', isEqualTo: 'active');

    final snapshot = await query.get();

    return snapshot.docs
        .map((doc) => ShowtimeModel.fromFirestore(doc))
        .where((showtime) =>
            excludeShowtimeId == null || showtime.id != excludeShowtimeId)
        .toList();
  }

  
  Future<List<String>> getAvailableTimeSlots({
    required String theaterId,
    required String screenId,
    required String date,
    required Duration movieDuration,
    String? excludeShowtimeId,
  }) async {
    return await _generateSuggestedTimes(
        theaterId, screenId, date, movieDuration, excludeShowtimeId);
  }

  
  Future<Map<String, List<ShowtimeModel>>> getTheaterScheduleForDate(
    String theaterId,
    String date,
  ) async {
    final snapshot = await _firestore
        .collection(_collection)
        .where('theaterId', isEqualTo: theaterId)
        .where('date', isEqualTo: date)
        .where('status', isEqualTo: 'active')
        .orderBy('time')
        .get();

    final showtimes =
        snapshot.docs.map((doc) => ShowtimeModel.fromFirestore(doc)).toList();

    
    Map<String, List<ShowtimeModel>> scheduleByScreen = {};
    for (final showtime in showtimes) {
      if (!scheduleByScreen.containsKey(showtime.screenId)) {
        scheduleByScreen[showtime.screenId] = [];
      }
      scheduleByScreen[showtime.screenId]!.add(showtime);
    }

    return scheduleByScreen;
  }

  
  Future<Map<String, Map<String, List<ShowtimeModel>>>>
      getTheaterScheduleForDateRange(
    String theaterId,
    String startDate,
    String endDate,
  ) async {
    final snapshot = await _firestore
        .collection(_collection)
        .where('theaterId', isEqualTo: theaterId)
        .where('date', isGreaterThanOrEqualTo: startDate)
        .where('date', isLessThanOrEqualTo: endDate)
        .where('status', isEqualTo: 'active')
        .orderBy('date')
        .orderBy('time')
        .get();

    final showtimes =
        snapshot.docs.map((doc) => ShowtimeModel.fromFirestore(doc)).toList();

    
    Map<String, Map<String, List<ShowtimeModel>>> scheduleByDate = {};
    for (final showtime in showtimes) {
      if (!scheduleByDate.containsKey(showtime.date)) {
        scheduleByDate[showtime.date] = {};
      }
      if (!scheduleByDate[showtime.date]!.containsKey(showtime.screenId)) {
        scheduleByDate[showtime.date]![showtime.screenId] = [];
      }
      scheduleByDate[showtime.date]![showtime.screenId]!.add(showtime);
    }

    return scheduleByDate;
  }

  
  String calculateEndTime(String startTime, Duration movieDuration) {
    final startDateTime = DateTime.parse('2024-01-01 $startTime:00');
    final endDateTime = startDateTime.add(movieDuration);
    return _formatTime(endDateTime);
  }

  
  Duration getMovieDuration(Movie movie) {
    if (movie.runtime != null && movie.runtime! > 0) {
      return Duration(minutes: movie.runtime!);
    }

    
    return _standardDurations['standard']!;
  }

  
  Future<List<ScheduleConflict>> bulkCreateSchedule({
    required int movieId,
    required String theaterId,
    required List<String> screenIds,
    required List<String> dates,
    required List<String> times,
    required Duration movieDuration,
  }) async {
    List<ScheduleConflict> conflicts = [];

    for (final screenId in screenIds) {
      for (final date in dates) {
        for (final time in times) {
          final conflict = await checkTimeSlotAvailability(
            theaterId: theaterId,
            screenId: screenId,
            date: date,
            startTime: time,
            movieDuration: movieDuration,
          );

          if (conflict != null) {
            conflicts.add(conflict);
          }
        }
      }
    }

    return conflicts;
  }

  
  DateTime _parseDateTime(String date, String time) {
    return DateTime.parse('$date $time:00');
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  
  Future<List<String>> getPopularTimeSlots() async {
    
    return [
      '09:00',
      '10:30',
      '12:00',
      '13:30',
      '15:00',
      '16:30',
      '18:00',
      '19:30',
      '21:00',
      '22:30'
    ];
  }

  
  bool isValidScheduleDate(String date) {
    final scheduleDate = DateTime.parse(date);
    final now = DateTime.now();
    final maxFutureDate = now.add(const Duration(days: 90)); 

    return scheduleDate.isAfter(now.subtract(const Duration(days: 1))) &&
        scheduleDate.isBefore(maxFutureDate);
  }
}

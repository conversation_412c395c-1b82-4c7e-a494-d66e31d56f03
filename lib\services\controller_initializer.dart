import 'package:get/get.dart';
import '../controllers/movie_controller.dart';
import '../controllers/favorite_controller.dart';
import '../controllers/ticket_controller.dart';
import '../controllers/booking_controller.dart';
import '../controllers/schedule_controller.dart';
import '../controllers/banner_controller.dart';

import '../controllers/realtime_notification_controller.dart';
import '../controllers/realtime_bug_report_controller.dart';
import '../bindings/realtime_database_binding.dart';
import '../utils/developer_mode.dart';

class ControllerInitializer {
  static bool _isInitialized = false;

  
  static Future<void> initializeControllers() async {
    if (_isInitialized) return;

    try {
      
      Get.put(MovieController());
      Get.put(FavoriteController());
      Get.put(TicketController());

      
      Get.put(BookingController());
      Get.put(ScheduleController());

      
      Get.put(BannerController());

      
      Get.put(RealtimeNotificationController());
      Get.put(RealtimeBugReportController());

      
      Get.put(DeveloperMode());

      
      RealtimeDatabaseBinding().dependencies();

      _isInitialized = true;
    } catch (e) {
      
      print('Error initializing controllers: $e');
    }
  }

  
  static void reset() {
    _isInitialized = false;
  }

  
  static bool get isInitialized => _isInitialized;

  
  static T safeGet<T>() {
    try {
      return Get.find<T>();
    } catch (e) {
      
      if (!_isInitialized) {
        initializeControllers();
      }

      
      try {
        return Get.find<T>();
      } catch (e2) {
        
        if (T == MovieController) {
          return Get.put(MovieController()) as T;
        } else if (T == FavoriteController) {
          return Get.put(FavoriteController()) as T;
        } else if (T == TicketController) {
          return Get.put(TicketController()) as T;
        } else if (T == BookingController) {
          return Get.put(BookingController()) as T;
        } else if (T == ScheduleController) {
          return Get.put(ScheduleController()) as T;
        } else if (T == BannerController) {
          return Get.put(BannerController()) as T;
        } else if (T == RealtimeNotificationController) {
          return Get.put(RealtimeNotificationController()) as T;
        } else if (T == RealtimeBugReportController) {
          return Get.put(RealtimeBugReportController()) as T;
        } else if (T == DeveloperMode) {
          return Get.put(DeveloperMode()) as T;
        } else {
          rethrow;
        }
      }
    }
  }
}

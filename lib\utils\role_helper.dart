import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

class RoleHelper {
  
  static bool isAdmin() {
    final authController = Get.find<AuthController>();
    
    
    if (!authController.isLoggedIn) {
      return false;
    }
    
    
    final userRole = authController.userRole;
    return userRole == 'admin' || userRole == 'developer';
  }
  
  
  static bool isDeveloper() {
    final authController = Get.find<AuthController>();
    
    
    if (!authController.isLoggedIn) {
      return false;
    }
    
    
    final userRole = authController.userRole;
    return userRole == 'developer';
  }
  
  
  static bool hasAdminAccess() {
    return isAdmin() || isDeveloper();
  }
}

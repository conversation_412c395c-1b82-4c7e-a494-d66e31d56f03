import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/showtime_model.dart';

class ShowtimeStatusService {
  static final ShowtimeStatusService _instance = ShowtimeStatusService._internal();
  factory ShowtimeStatusService() => _instance;
  ShowtimeStatusService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'showtimes';
  
  Timer? _statusUpdateTimer;
  bool _isRunning = false;

  
  void startAutoStatusUpdate({Duration interval = const Duration(minutes: 5)}) {
    if (_isRunning) {
      print('ShowtimeStatusService: Already running');
      return;
    }

    print('ShowtimeStatusService: Starting auto status update with interval: ${interval.inMinutes} minutes');
    _isRunning = true;

    
    _updateAllShowtimeStatuses();

    
    _statusUpdateTimer = Timer.periodic(interval, (timer) {
      _updateAllShowtimeStatuses();
    });
  }

  
  void stopAutoStatusUpdate() {
    if (!_isRunning) return;

    print('ShowtimeStatusService: Stopping auto status update');
    _statusUpdateTimer?.cancel();
    _statusUpdateTimer = null;
    _isRunning = false;
  }

  
  Future<void> _updateAllShowtimeStatuses() async {
    try {
      print('ShowtimeStatusService: Starting status update check...');
      
      
      final snapshot = await _firestore
          .collection(_collection)
          .where('status', whereIn: ['upcoming', 'ongoing', 'active']) // Include 'active' for backward compatibility
          .get();

      int updatedCount = 0;
      int checkedCount = 0;

      final batch = _firestore.batch();
      final now = DateTime.now();

      for (final doc in snapshot.docs) {
        try {
          final showtime = ShowtimeModel.fromFirestore(doc);
          checkedCount++;

          if (showtime.needsStatusUpdate) {
            final newStatus = showtime.timeBasedStatus;
            
            print('ShowtimeStatusService: Updating showtime ${showtime.id} from ${showtime.status.name} to ${newStatus.name}');
            
            batch.update(doc.reference, {
              'status': newStatus.name,
              'updatedAt': Timestamp.fromDate(now),
            });
            
            updatedCount++;
          }
        } catch (e) {
          print('ShowtimeStatusService: Error processing showtime ${doc.id}: $e');
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        print('ShowtimeStatusService: Updated $updatedCount out of $checkedCount showtimes');
      } else {
        print('ShowtimeStatusService: No updates needed for $checkedCount showtimes');
      }

    } catch (e) {
      print('ShowtimeStatusService: Error during status update: $e');
    }
  }

  
  Future<void> updateShowtimeStatus(String showtimeId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(showtimeId).get();
      
      if (!doc.exists) {
        print('ShowtimeStatusService: Showtime $showtimeId not found');
        return;
      }

      final showtime = ShowtimeModel.fromFirestore(doc);
      
      if (showtime.needsStatusUpdate) {
        final newStatus = showtime.timeBasedStatus;
        
        await doc.reference.update({
          'status': newStatus.name,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
        
        print('ShowtimeStatusService: Updated showtime $showtimeId from ${showtime.status.name} to ${newStatus.name}');
      }
    } catch (e) {
      print('ShowtimeStatusService: Error updating showtime $showtimeId: $e');
    }
  }

  
  Future<void> updateMovieShowtimeStatuses(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('status', whereIn: ['upcoming', 'ongoing', 'active'])
          .get();

      if (snapshot.docs.isEmpty) return;

      final batch = _firestore.batch();
      final now = DateTime.now();
      int updatedCount = 0;

      for (final doc in snapshot.docs) {
        try {
          final showtime = ShowtimeModel.fromFirestore(doc);
          
          if (showtime.needsStatusUpdate) {
            final newStatus = showtime.timeBasedStatus;
            
            batch.update(doc.reference, {
              'status': newStatus.name,
              'updatedAt': Timestamp.fromDate(now),
            });
            
            updatedCount++;
          }
        } catch (e) {
          print('ShowtimeStatusService: Error processing showtime ${doc.id}: $e');
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        print('ShowtimeStatusService: Updated $updatedCount showtimes for movie $movieId');
      }
    } catch (e) {
      print('ShowtimeStatusService: Error updating movie showtimes: $e');
    }
  }

  
  Future<void> updateTheaterShowtimeStatuses(String theaterId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('theaterId', isEqualTo: theaterId)
          .where('status', whereIn: ['upcoming', 'ongoing', 'active'])
          .get();

      if (snapshot.docs.isEmpty) return;

      final batch = _firestore.batch();
      final now = DateTime.now();
      int updatedCount = 0;

      for (final doc in snapshot.docs) {
        try {
          final showtime = ShowtimeModel.fromFirestore(doc);
          
          if (showtime.needsStatusUpdate) {
            final newStatus = showtime.timeBasedStatus;
            
            batch.update(doc.reference, {
              'status': newStatus.name,
              'updatedAt': Timestamp.fromDate(now),
            });
            
            updatedCount++;
          }
        } catch (e) {
          print('ShowtimeStatusService: Error processing showtime ${doc.id}: $e');
        }
      }

      if (updatedCount > 0) {
        await batch.commit();
        print('ShowtimeStatusService: Updated $updatedCount showtimes for theater $theaterId');
      }
    } catch (e) {
      print('ShowtimeStatusService: Error updating theater showtimes: $e');
    }
  }

  
  Future<Map<String, int>> getShowtimeStatusStats() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      
      final stats = <String, int>{
        'upcoming': 0,
        'ongoing': 0,
        'ended': 0,
        'cancelled': 0,
        'full': 0,
        'needsUpdate': 0,
      };

      for (final doc in snapshot.docs) {
        try {
          final showtime = ShowtimeModel.fromFirestore(doc);
          final currentStatus = showtime.status.name;
          final timeBasedStatus = showtime.timeBasedStatus.name;
          
          stats[currentStatus] = (stats[currentStatus] ?? 0) + 1;
          
          if (showtime.needsStatusUpdate) {
            stats['needsUpdate'] = (stats['needsUpdate'] ?? 0) + 1;
          }
        } catch (e) {
          print('ShowtimeStatusService: Error processing showtime ${doc.id}: $e');
        }
      }

      return stats;
    } catch (e) {
      print('ShowtimeStatusService: Error getting stats: $e');
      return {};
    }
  }

  
  bool get isRunning => _isRunning;

  
  void dispose() {
    stopAutoStatusUpdate();
  }
}

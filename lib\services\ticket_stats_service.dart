import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/ticket_model.dart';
import '../models/movie_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../services/firebase_movie_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class TicketStatsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMovieService _movieService = FirebaseMovieService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  
  Future<List<MovieTicketStats>> getAllMovieTicketStats() async {
    try {
      
      final futures = await Future.wait([
        _movieService.getMovies(),
        _getAllTickets(),
        _getAllShowtimesWithCapacity(),
      ]);

      final movies = futures[0] as List<Movie>;
      final allTickets = futures[1] as List<Ticket>;
      final showtimeCapacities = futures[2] as Map<int, int>;

      
      final ticketsByMovie = <int, List<Ticket>>{};
      for (final ticket in allTickets) {
        ticketsByMovie.putIfAbsent(ticket.movieId, () => []).add(ticket);
      }

      
      final List<MovieTicketStats> statsList = [];
      for (final movie in movies) {
        final movieTickets = ticketsByMovie[movie.id] ?? [];
        final totalCapacity = showtimeCapacities[movie.id] ?? 0;

        final stats = MovieTicketStats.fromData(
          movieId: movie.id,
          movieTitle: movie.title,
          moviePosterPath: movie.posterPath,
          totalAvailableTickets: totalCapacity,
          tickets: movieTickets,
        );
        statsList.add(stats);
      }

      
      statsList.sort((a, b) => a.movieTitle.compareTo(b.movieTitle));
      return statsList;
    } catch (e) {
      throw Exception('Failed to get movie ticket stats: $e');
    }
  }

  
  Future<List<Ticket>> _getAllTickets() async {
    try {
      final snapshot = await _firestore.collection('tickets').get();
      final tickets =
          snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      print('Total tickets in database: ${tickets.length}');

      
      final statusCounts = <String, int>{};
      for (final ticket in tickets) {
        statusCounts[ticket.status.name] =
            (statusCounts[ticket.status.name] ?? 0) + 1;
      }
      print('Tickets by status: $statusCounts');

      return tickets;
    } catch (e) {
      throw Exception('Failed to get all tickets: $e');
    }
  }

  
  Future<Map<int, int>> _getAllShowtimesWithCapacity() async {
    try {
      
      final futures = await Future.wait([
        _showtimeService.getAllShowtimes(),
        _screenService.getAllScreens(activeOnly: false), 
      ]);

      final showtimes = futures[0] as List<ShowtimeModel>;
      final screens = futures[1] as List<ScreenModel>;

      
      final screenCapacities = <String, int>{};
      for (final screen in screens) {
        screenCapacities[screen.id] = screen.totalSeats;
      }

      
      final capacities = <int, int>{};
      for (final showtime in showtimes) {
        final screenCapacity = screenCapacities[showtime.screenId] ?? 0;
        if (screenCapacity > 0) {
          
          capacities[showtime.movieId] =
              (capacities[showtime.movieId] ?? 0) + screenCapacity;
        }
      }

      return capacities;
    } catch (e) {
      print('Error getting showtime capacities: $e');
      
      return {};
    }
  }

  
  Future<List<MovieTicketStats>> getMovieListWithLazyStats() async {
    try {
      
      final movies = await _movieService.getMovies();

      
      return movies
          .map((movie) => MovieTicketStats(
                movieId: movie.id,
                movieTitle: movie.title,
                moviePosterPath: movie.posterPath,
                totalAvailableTickets: 0,
                soldTickets: 0,
                remainingTickets: 0,
                totalRevenue: 0.0,
                tickets: [],
              ))
          .toList();
    } catch (e) {
      throw Exception('Failed to get movie list: $e');
    }
  }

  
  Future<MovieTicketStats?> updateMovieStats(
      MovieTicketStats movieStats) async {
    try {
      final updatedStats = await getMovieTicketStats(movieStats.movieId);
      return updatedStats;
    } catch (e) {
      
      return movieStats;
    }
  }

  
  Future<MovieTicketStats?> getMovieTicketStats(int movieId) async {
    try {
      
      final movie = await _movieService.getMovieByMovieId(movieId);
      if (movie == null) return null;

      
      final ticketsSnapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .get();

      final tickets =
          ticketsSnapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      
      final totalAvailableTickets =
          await _calculateTotalAvailableTickets(movieId);

      return MovieTicketStats.fromData(
        movieId: movieId,
        movieTitle: movie.title,
        moviePosterPath: movie.posterPath,
        totalAvailableTickets: totalAvailableTickets,
        tickets: tickets,
      );
    } catch (e) {
      throw Exception(
          'Failed to get movie ticket stats for movie $movieId: $e');
    }
  }

  
  Future<int> _calculateTotalAvailableTickets(int movieId) async {
    try {
      
      final showtimes = await _showtimeService.getShowtimesByMovie(movieId);
      int totalTickets = 0;

      for (final showtime in showtimes) {
        
        final screen = await _screenService.getScreenById(showtime.screenId);
        if (screen != null && screen.totalSeats > 0) {
          totalTickets += screen.totalSeats;
        }
      }

      print(
          'Movie $movieId: ${showtimes.length} showtimes, $totalTickets total seats');
      return totalTickets;
    } catch (e) {
      print('Error calculating total available tickets for movie $movieId: $e');
      
      return 0;
    }
  }

  
  Future<List<Ticket>> getMovieTickets(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .orderBy('purchaseDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get movie tickets: $e');
    }
  }

  
  Future<List<Map<String, dynamic>>> getMovieTicketsWithUserInfo(
      int movieId) async {
    try {
      final tickets = await getMovieTickets(movieId);
      final List<Map<String, dynamic>> ticketsWithUserInfo = [];

      for (final ticket in tickets) {
        
        String userName = 'Unknown User';
        String userEmail = '';

        try {
          final userDoc =
              await _firestore.collection('users').doc(ticket.userId).get();

          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            userName =
                userData['displayName'] ?? userData['name'] ?? 'Unknown User';
            userEmail = userData['email'] ?? '';
          }
        } catch (e) {
          
        }

        ticketsWithUserInfo.add({
          'ticket': ticket,
          'userName': userName,
          'userEmail': userEmail,
        });
      }

      return ticketsWithUserInfo;
    } catch (e) {
      throw Exception('Failed to get movie tickets with user info: $e');
    }
  }

  
  Future<void> cancelTicket(String ticketId, {double? refundAmount}) async {
    try {
      
      final ticketDoc =
          await _firestore.collection('tickets').doc(ticketId).get();

      if (!ticketDoc.exists) {
        throw Exception('Ticket not found');
      }

      final ticket = Ticket.fromFirestore(ticketDoc);

      
      await _firestore.collection('tickets').doc(ticketId).update({
        'status': TicketStatus.cancelled.name,
        'cancelledAt': Timestamp.now(),
        'refundAmount': refundAmount ?? 0.0,
      });

      
      if (ticket.seats.isNotEmpty) {
        final seatIds = ticket.seats.map((seat) => seat.seatId).toList();

        print(
            'Admin cancelling ticket ${ticket.bookingCode}, releasing seats: $seatIds');
        await _showtimeService.releaseBookedSeats(ticket.showtimeId, seatIds);
        print('Successfully released seats for admin-cancelled ticket');
      }
    } catch (e) {
      print('Error cancelling ticket: $e');
      throw Exception('Failed to cancel ticket: $e');
    }
  }

  
  Future<Map<String, int>> cleanupCancelledTicketsSeats() async {
    try {
      print('Starting cleanup of seats for cancelled tickets...');

      
      final cancelledTicketsSnapshot = await _firestore
          .collection('tickets')
          .where('status', isEqualTo: TicketStatus.cancelled.name)
          .get();

      print('Found ${cancelledTicketsSnapshot.docs.length} cancelled tickets');

      int processedCount = 0;
      int releasedSeatsCount = 0;
      int errorCount = 0;

      for (final doc in cancelledTicketsSnapshot.docs) {
        try {
          final ticket = Ticket.fromFirestore(doc);

          if (ticket.seats.isNotEmpty) {
            final seatIds = ticket.seats.map((seat) => seat.seatId).toList();

            print(
                'Processing cancelled ticket ${ticket.bookingCode} with seats: $seatIds');

            
            try {
              await _showtimeService.releaseBookedSeats(
                  ticket.showtimeId, seatIds);
              print('✓ Released seats for ticket ${ticket.bookingCode}');
              processedCount++;
              releasedSeatsCount += seatIds.length;
            } catch (e) {
              print(
                  '⚠ Could not release seats for ticket ${ticket.bookingCode}: $e');
              
            }
          }
        } catch (e) {
          print('✗ Error processing ticket ${doc.id}: $e');
          errorCount++;
        }
      }

      print(
          'Cleanup completed: $processedCount tickets processed, $releasedSeatsCount seats released, $errorCount errors');

      return {
        'processed': processedCount,
        'released': releasedSeatsCount,
        'errors': errorCount,
      };
    } catch (e) {
      print('Error during cleanup: $e');
      throw Exception('Failed to cleanup cancelled tickets seats: $e');
    }
  }

  
  Future<Map<String, dynamic>> getOverallStats() async {
    try {
      final allStats = await getAllMovieTicketStats();

      int totalMovies = allStats.length;
      int totalAvailableSeats = allStats.fold(
          0, (total, stats) => total + stats.totalAvailableTickets);
      int totalSoldSeats =
          allStats.fold(0, (total, stats) => total + stats.soldTickets);
      int totalRemainingSeats =
          allStats.fold(0, (total, stats) => total + stats.remainingTickets);
      double totalRevenue =
          allStats.fold(0.0, (total, stats) => total + stats.totalRevenue);

      
      int totalTickets =
          allStats.fold(0, (total, stats) => total + stats.totalTickets);

      double overallOccupancyRate = totalAvailableSeats > 0
          ? (totalSoldSeats / totalAvailableSeats) * 100
          : 0.0;

      return {
        'totalMovies': totalMovies,
        'totalAvailableTickets':
            totalAvailableSeats, 
        'totalSoldTickets': totalSoldSeats, // Now represents seats sold
        'totalRemainingTickets': totalRemainingSeats,
        'totalTickets': totalTickets, // Total number of ticket transactions
        'totalRevenue': totalRevenue,
        'overallOccupancyRate': overallOccupancyRate,
      };
    } catch (e) {
      throw Exception('Failed to get overall stats: $e');
    }
  }

  
  Stream<List<MovieTicketStats>> getAllMovieTicketStatsStream() {
    return _firestore
        .collection('tickets')
        .snapshots()
        .asyncMap((_) => getAllMovieTicketStats());
  }
}
